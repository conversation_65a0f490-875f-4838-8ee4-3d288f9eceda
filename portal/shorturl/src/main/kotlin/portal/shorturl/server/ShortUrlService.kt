package portal.shorturl.server

import com.google.rpc.Status
import common.libs.logger.Logging
import io.grpc.Status.Code
import org.koin.core.annotation.Singleton
import portal.shorturl.models.ShortenRequest
import portal.shorturl.services.URLService
import proto.portal.shorturl.*
import java.util.*

/**
 * gRPC service implementation for ShortURL service
 *
 * <AUTHOR>
 */
@Singleton
class ShortUrlService(
    private val urlService: URLService
) : ShortUrlServiceGrpcKt.ShortUrlServiceCoroutineImplBase(), Logging {

    override suspend fun shortenUrl(request: ShortenUrlRequest): ShortenUrlResponse {
        logger.debug("gRPC shortenUrl request: {}", request)
        
        val builder = ShortenUrlResponse.newBuilder()
        var code = Code.OK
        var message = ""

        try {
            val shortenRequest = ShortenRequest(url = request.url)
            val clientIP = request.clientIp ?: "unknown"
            val response = urlService.shortenUrl(shortenRequest, clientIP)
            
            builder.setShortUrl(response.short_url)
            builder.setOriginal(response.original)
            
        } catch (e: IllegalArgumentException) {
            code = Code.INVALID_ARGUMENT
            message = e.message ?: "Invalid request"
            logger.warn("Invalid argument in shortenUrl: {}", e.message)
        } catch (e: Exception) {
            code = Code.INTERNAL
            message = "Internal server error"
            logger.error("Error in shortenUrl", e)
        }

        val status = Status.newBuilder()
            .setCode(code.value())
            .setMessage(message)
            .build()
        
        builder.setStatus(status)
        val response = builder.build()
        logger.debug("gRPC shortenUrl response: {}", response)
        return response
    }

    override suspend fun getUrlStats(request: GetUrlStatsRequest): GetUrlStatsResponse {
        logger.debug("gRPC getUrlStats request: {}", request)
        
        val builder = GetUrlStatsResponse.newBuilder()
        var code = Code.OK
        var message = ""

        try {
            val clientIP = request.clientIp ?: "unknown"
            val urlDocument = urlService.getUrlStats(request.shortCode, clientIP)
            
            val urlDocumentProto = URLDocumentProto.newBuilder()
                .setId(urlDocument.id ?: "")
                .setOriginal(urlDocument.original)
                .setShort(urlDocument.short)
                .setCreated(urlDocument.created.time)
                .setClicks(urlDocument.clicks)
                .build()
            
            builder.setUrlDocument(urlDocumentProto)
            
        } catch (e: NoSuchElementException) {
            code = Code.NOT_FOUND
            message = "URL not found"
            logger.warn("URL not found in getUrlStats: {}", e.message)
        } catch (e: Exception) {
            code = Code.INTERNAL
            message = "Internal server error"
            logger.error("Error in getUrlStats", e)
        }

        val status = Status.newBuilder()
            .setCode(code.value())
            .setMessage(message)
            .build()
        
        builder.setStatus(status)
        val response = builder.build()
        logger.debug("gRPC getUrlStats response: {}", response)
        return response
    }

    override suspend fun redirectUrl(request: RedirectUrlRequest): RedirectUrlResponse {
        logger.debug("gRPC redirectUrl request: {}", request)
        
        val builder = RedirectUrlResponse.newBuilder()
        var code = Code.OK
        var message = ""

        try {
            val clientIP = request.clientIp ?: "unknown"
            val userAgent = request.userAgent ?: ""
            val referer = request.referer ?: ""
            
            val redirectResult = urlService.redirectUrl(
                request.shortCode, 
                request.currentHost, 
                clientIP, 
                userAgent, 
                referer
            )
            
            when (redirectResult) {
                is portal.shorturl.services.RedirectResult.SameDomain -> {
                    builder.setRedirectUrl(redirectResult.url)
                    builder.setIsSameDomain(true)
                }
                is portal.shorturl.services.RedirectResult.ExternalDomain -> {
                    builder.setRedirectUrl(redirectResult.url)
                    builder.setIsSameDomain(false)
                }
            }
            
        } catch (e: NoSuchElementException) {
            code = Code.NOT_FOUND
            message = "URL not found"
            logger.warn("URL not found in redirectUrl: {}", e.message)
        } catch (e: Exception) {
            code = Code.INTERNAL
            message = "Internal server error"
            logger.error("Error in redirectUrl", e)
        }

        val status = Status.newBuilder()
            .setCode(code.value())
            .setMessage(message)
            .build()
        
        builder.setStatus(status)
        val response = builder.build()
        logger.debug("gRPC redirectUrl response: {}", response)
        return response
    }

    override suspend fun getUrlsPaginated(request: GetUrlsPaginatedRequest): GetUrlsPaginatedResponse {
        logger.debug("gRPC getUrlsPaginated request: {}", request)

        val builder = GetUrlsPaginatedResponse.newBuilder()
        var code = Code.OK
        var message = ""

        try {
            val urlListResponse = urlService.getUrlsPaginated(request.page, request.limit)

            val urlProtos = urlListResponse.urls.map { urlDocument ->
                URLDocumentProto.newBuilder()
                    .setId(urlDocument.id ?: "")
                    .setOriginal(urlDocument.original)
                    .setShort(urlDocument.short)
                    .setCreated(urlDocument.created.time)
                    .setClicks(urlDocument.clicks)
                    .build()
            }

            builder.addAllUrls(urlProtos)
            builder.setPage(urlListResponse.page)
            builder.setLimit(urlListResponse.limit)
            builder.setTotal(urlListResponse.total)
            builder.setTotalPages(urlListResponse.totalPages)

        } catch (e: Exception) {
            code = Code.INTERNAL
            message = "Internal server error"
            logger.error("Error in getUrlsPaginated", e)
        }

        val status = Status.newBuilder()
            .setCode(code.value())
            .setMessage(message)
            .build()

        builder.setStatus(status)
        val response = builder.build()
        logger.debug("gRPC getUrlsPaginated response: {}", response)
        return response
    }

    override suspend fun searchUrls(request: SearchUrlsRequest): SearchUrlsResponse {
        logger.debug("gRPC searchUrls request: {}", request)

        val builder = SearchUrlsResponse.newBuilder()
        var code = Code.OK
        var message = ""

        try {
            val searchResponse = urlService.searchUrlsPaginated(request.query, request.page, request.limit)

            val urlProtos = searchResponse.urls.map { urlDocument ->
                URLDocumentProto.newBuilder()
                    .setId(urlDocument.id ?: "")
                    .setOriginal(urlDocument.original)
                    .setShort(urlDocument.short)
                    .setCreated(urlDocument.created.time)
                    .setClicks(urlDocument.clicks)
                    .build()
            }

            builder.addAllUrls(urlProtos)
            builder.setPage(searchResponse.page)
            builder.setLimit(searchResponse.limit)
            builder.setTotal(searchResponse.total)
            builder.setTotalPages(searchResponse.totalPages)
            builder.setQuery(searchResponse.query)

        } catch (e: Exception) {
            code = Code.INTERNAL
            message = "Internal server error"
            logger.error("Error in searchUrls", e)
        }

        val status = Status.newBuilder()
            .setCode(code.value())
            .setMessage(message)
            .build()

        builder.setStatus(status)
        val response = builder.build()
        logger.debug("gRPC searchUrls response: {}", response)
        return response
    }

    override suspend fun deleteUrl(request: DeleteUrlRequest): DeleteUrlResponse {
        logger.debug("gRPC deleteUrl request: {}", request)

        val builder = DeleteUrlResponse.newBuilder()
        var code = Code.OK
        var message = ""

        try {
            val success = urlService.deleteUrl(request.shortCode)
            builder.setSuccess(success)

            if (!success) {
                code = Code.NOT_FOUND
                message = "URL not found"
            }

        } catch (e: Exception) {
            code = Code.INTERNAL
            message = "Internal server error"
            logger.error("Error in deleteUrl", e)
        }

        val status = Status.newBuilder()
            .setCode(code.value())
            .setMessage(message)
            .build()

        builder.setStatus(status)
        val response = builder.build()
        logger.debug("gRPC deleteUrl response: {}", response)
        return response
    }
}
