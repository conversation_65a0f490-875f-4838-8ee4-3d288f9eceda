package portal.shorturl.services

import common.libs.logger.Logging
import org.koin.core.annotation.Singleton
import portal.shorturl.configuration.Configuration
import portal.shorturl.dbgateway.URLGateway
import portal.shorturl.models.*
import portal.shorturl.utility.ShortCodeGenerator
import portal.shorturl.utility.URLUtils
import java.util.*

@Singleton
class URLService(
    private val urlGateway: URLGateway, private val config: Configuration
) : Logging {

    suspend fun shortenUrl(
        request: ShortenRequest, clientIP: String
    ): ShortenResponse {
        logger.info("[SHORTEN] Request started from $clientIP")

        if (request.url.isBlank()) {
            logger.warn("[SHORTEN] Empty URL provided from $clientIP")
            throw IllegalArgumentException("URL is required")
        }

        // Normalize URL (remove trailing slashes, etc.)
        val normalizedUrl = URLUtils.normalizeUrl(request.url)

        val isValid = isValidUrl(normalizedUrl) || isValidPath(normalizedUrl)
        if (!isValid) {
            logger.warn("[SHORTEN] Invalid URL or path provided from $clientIP: ${request.url}")
            throw IllegalArgumentException("Invalid URL or path format")
        }

        logger.info("[SHORTEN] Processing normalized URL or path: $normalizedUrl from $clientIP")

        // Check if URL already exists
        val urlDoc = urlGateway.findByOriginalUrl(normalizedUrl) ?: createUniqueShortUrlWithRetry(normalizedUrl)
        val response = ShortenResponse(
            short_url = "${config.baseUrl}/${urlDoc.short}", original = urlDoc.original
        )
        logger.info("[SHORTEN] Successfully created/retrieved short URL: $normalizedUrl -> ${response.short_url}")
        return response
    }

    private fun getBase62Timestamp(): String {
        val base62Chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
        var timestamp = System.currentTimeMillis()
        val sb = StringBuilder()
        do {
            sb.append(base62Chars[(timestamp % 62).toInt()])
            timestamp /= 62
        } while (timestamp > 0)
        return sb.reverse().toString()
    }

    private suspend fun createUniqueShortUrlWithRetry(originalUrl: String, maxRetries: Int = 5): URLDocument {
        logger.debug("[SHORTEN] Creating new short URL for: $originalUrl with collision check")
        var attempt = 0
        while (attempt < maxRetries) {
            val shortCode = ShortCodeGenerator.generateShortCode() + getBase62Timestamp()
            val urlDocument = URLDocument(
                original = originalUrl, short = shortCode, created = Date(), clicks = 0
            )
            try {
                return urlGateway.insertUrl(urlDocument)
            } catch (error: Exception) {
                if (error.message?.contains("duplicate", ignoreCase = true) == true) {
                    attempt++
                    logger.warn("[SHORTEN] Collision detected for short code $shortCode, retrying (attempt $attempt)")
                } else {
                    logger.error("[SHORTEN] Failed to insert short URL after $attempt attempts", error)
                    throw error
                }
            }
        }
        throw Exception("Failed to create unique short URL after $maxRetries attempts")
    }

    suspend fun redirectUrl(
        shortCode: String, currentHost: String, clientIP: String, userAgent: String, referer: String
    ): RedirectResult {
        logger.info("[REDIRECT] Request for short code: $shortCode from $clientIP (User-Agent: $userAgent, Referer: $referer)")

        val urlDoc = urlGateway.findByShortCode(shortCode)
            ?: throw NoSuchElementException("URL not found")
        logger.info("[REDIRECT] Found URL: $shortCode -> ${urlDoc.original} (current clicks: ${urlDoc.clicks})")

        try {
            urlGateway.incrementClickCount(shortCode)
            logger.debug("[REDIRECT] Successfully incremented click count for $shortCode")
        } catch (error: Exception) {
            logger.warn("[REDIRECT] Error updating click count for $shortCode", error)
        }

        val isPath = urlDoc.original.startsWith("/")
        val sameDomain = isPath || URLUtils.isSameDomain(config.baseUrl, urlDoc.original)
        logger.info("[REDIRECT] Domain check - Base: ${config.baseUrl}, Target: ${urlDoc.original}, Same domain: $sameDomain")

        val result = if (sameDomain) {
            RedirectResult.SameDomain(urlDoc.original)
        } else {
            RedirectResult.ExternalDomain(urlDoc.original)
        }
        when (result) {
            is RedirectResult.SameDomain -> logger.info("[REDIRECT] Redirecting to same domain URL: $shortCode -> ${result.url}")
            is RedirectResult.ExternalDomain -> logger.info("[REDIRECT] Showing warning page for external redirect: $shortCode -> ${result.url}")
        }
        return result
    }

    suspend fun getUrlStats(shortCode: String, clientIP: String): URLDocument {
        logger.info("[STATS] Request for stats of short code: $shortCode from $clientIP")
        val urlDoc = urlGateway.findByShortCode(shortCode)
            ?: throw NoSuchElementException("URL not found")
        logger.info("[STATS] Found URL stats: $shortCode -> ${urlDoc.original} (clicks: ${urlDoc.clicks}, created: ${urlDoc.created})")
        return urlDoc
    }

    suspend fun getUrlsPaginated(page: Int = 1, limit: Int = 10): URLListResponse {
        logger.debug("[URLS] Fetching URLs with pagination - page: $page, limit: $limit")
        val paginatedResponse = urlGateway.getUrlsPaginated(page, limit)
        val response = URLListResponse(
            urls = paginatedResponse.data,
            page = paginatedResponse.page,
            limit = paginatedResponse.limit,
            total = paginatedResponse.total,
            totalPages = paginatedResponse.totalPages
        )
        logger.info("[URLS] Retrieved ${response.urls.size} URLs (page ${response.page}/${response.totalPages})")
        return response
    }

    suspend fun searchUrlsPaginated(query: String, page: Int = 1, limit: Int = 10): SearchResponse {
        logger.debug("[SEARCH] Searching URLs with pagination - query: $query, page: $page, limit: $limit")
        val paginatedResponse = urlGateway.searchUrlsPaginated(query, page, limit)
        val response = SearchResponse(
            urls = paginatedResponse.data,
            page = paginatedResponse.page,
            limit = paginatedResponse.limit,
            total = paginatedResponse.total,
            totalPages = paginatedResponse.totalPages,
            query = query
        )
        logger.info("[SEARCH] Found ${response.urls.size} URLs matching '$query' (page ${response.page}/${response.totalPages})")
        return response
    }

    suspend fun deleteUrl(shortCode: String): Boolean {
        logger.info("[DELETE] Deleting URL with short code: $shortCode")
        val success = urlGateway.deleteByShortCode(shortCode)
        if (success) {
            logger.info("[DELETE] Successfully deleted URL: $shortCode")
        } else {
            logger.warn("[DELETE] URL not found for deletion: $shortCode")
        }
        return success
    }

    private fun isValidUrl(url: String): Boolean {
        return URLUtils.isValidUrl(url)
    }

    private fun isValidPath(path: String): Boolean {
        // Path must start with '/', not be just '/', and not contain spaces or invalid characters
        if (!path.startsWith("/")) return false
        if (path == "/") return false
        // Disallow spaces and control characters
        if (path.any { it.isWhitespace() || it < ' ' }) return false
        // Optionally, disallow certain special characters (e.g., '?', '#')
        val invalidChars = listOf('?', '#')
        if (invalidChars.any { it in path }) return false
        return true
    }
}

sealed class RedirectResult {
    data class SameDomain(val url: String) : RedirectResult()
    data class ExternalDomain(val url: String) : RedirectResult()
}
