package portal.shorturl

import com.fasterxml.jackson.core.util.DefaultIndenter
import com.fasterxml.jackson.core.util.DefaultPrettyPrinter
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import common.libs.logger.Logging
import freemarker.cache.ClassTemplateLoader
import io.ktor.http.*
import io.ktor.serialization.jackson.*
import io.ktor.server.application.*
import io.ktor.server.freemarker.*
import io.ktor.server.logging.*
import io.ktor.server.netty.*
import io.ktor.server.plugins.callloging.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.plugins.statuspages.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.reactivex.rxjava3.plugins.RxJavaPlugins
import org.koin.ktor.plugin.Koin
import org.slf4j.event.Level
import portal.shorturl.koin.koinApplicationModule
import portal.shorturl.plugins.configureCORS
import portal.shorturl.plugins.configureRouting
import portal.shorturl.server.Server
import org.koin.core.context.GlobalContext
import kotlinx.coroutines.runBlocking
import kotlin.concurrent.thread

val logger = (object : Logging {
    override val loggerName = "SHORTURL_APPLICATION"
}).logger

fun Application.module() {
    install(ContentNegotiation) {
        jackson {
            setDefaultPrettyPrinter(DefaultPrettyPrinter().apply {
                indentArraysWith(DefaultPrettyPrinter.FixedSpaceIndenter.instance)
                indentObjectsWith(DefaultIndenter("  ", "\n"))
            })
            registerModule(JavaTimeModule())
        }
    }

    install(Koin) {
        modules(koinApplicationModule)
    }

    install(FreeMarker) {
        templateLoader = ClassTemplateLoader(this::class.java.classLoader, "templates")
    }

    configureRouting()
    configureCORS()

    install(StatusPages) {
        exception<Throwable> { call, cause ->
            logger.error("unknown exception... ${call.request.toLogString()}", cause)
            call.respondText(text = "500: InternalServerError", status = HttpStatusCode.InternalServerError)
        }
    }

    install(CallLogging) {
        level = Level.DEBUG
        format { call ->
            val status = call.response.status()
            val httpMethod = call.request.httpMethod.value
            val userAgent = call.request.headers["User-Agent"]
            val params = call.request.queryParameters.formUrlEncode()
            val path = call.request.path()
            "$status, $httpMethod $path, $userAgent, $params"
        }
    }
}

object Launcher : Logging {
    @JvmStatic
    fun main(args: Array<String>) {
        logger.info("Launching shorturl service")

        RxJavaPlugins.setErrorHandler {
            logger.error("Uncaught error... ", it)
        }

        // Start gRPC server in a separate thread
        thread(start = true, isDaemon = false, name = "grpc-server") {
            runBlocking {
                try {
                    // Wait for Koin to be initialized by the HTTP server
                    Thread.sleep(2000)
                    val grpcServer = GlobalContext.get().get<Server>()
                    grpcServer.start()
                    grpcServer.blockUntilShutdown()
                } catch (e: Exception) {
                    logger.error("Failed to start gRPC server", e)
                }
            }
        }

        // Start HTTP server
        EngineMain.main(args)
    }
}
