syntax = 'proto3';

package proto.portal.shorturl;

import "shorturl_messages.proto";

/**
 * Short URL service definitions
 * 
 * <AUTHOR>
 */

service ShortUrlService {
    rpc ShortenUrl (ShortenUrlRequest) returns (ShortenUrlResponse);
    rpc GetUrlStats (GetUrlStatsRequest) returns (GetUrlStatsResponse);
    rpc RedirectUrl (RedirectUrlRequest) returns (RedirectUrlResponse);
    rpc GetUrlsPaginated (GetUrlsPaginatedRequest) returns (GetUrlsPaginatedResponse);
    rpc SearchUrls (SearchUrlsRequest) returns (SearchUrlsResponse);
    rpc DeleteUrl (DeleteUrlRequest) returns (DeleteUrlResponse);
}
