syntax = 'proto3';

package proto.portal.shorturl;

import "google/rpc/status.proto";
import "google/protobuf/wrappers.proto";

/**
 * Short URL service message definitions
 * 
 * <AUTHOR>
 */

// URL Document Proto
message URLDocumentProto {
    string id = 1;
    string original = 2;
    string short = 3;
    int64 created = 4;
    int32 clicks = 5;
}

// Shorten URL Request/Response
message ShortenUrlRequest {
    string request_id = 1;
    string url = 2;
    optional string client_ip = 3;
}

message ShortenUrlResponse {
    google.rpc.Status status = 1;
    string short_url = 2;
    string original = 3;
}

// Get URL Stats Request/Response
message GetUrlStatsRequest {
    string request_id = 1;
    string short_code = 2;
    optional string client_ip = 3;
}

message GetUrlStatsResponse {
    google.rpc.Status status = 1;
    URLDocumentProto url_document = 2;
}

// Redirect URL Request/Response
message RedirectUrlRequest {
    string request_id = 1;
    string short_code = 2;
    string current_host = 3;
    optional string client_ip = 4;
    optional string user_agent = 5;
    optional string referer = 6;
}

message RedirectUrlResponse {
    google.rpc.Status status = 1;
    string redirect_url = 2;
    bool is_same_domain = 3;
}

// Get URLs Paginated Request/Response
message GetUrlsPaginatedRequest {
    string request_id = 1;
    int32 page = 2;
    int32 limit = 3;
}

message GetUrlsPaginatedResponse {
    google.rpc.Status status = 1;
    repeated URLDocumentProto urls = 2;
    int32 page = 3;
    int32 limit = 4;
    int64 total = 5;
    int32 total_pages = 6;
}

// Search URLs Request/Response
message SearchUrlsRequest {
    string request_id = 1;
    string query = 2;
    int32 page = 3;
    int32 limit = 4;
}

message SearchUrlsResponse {
    google.rpc.Status status = 1;
    repeated URLDocumentProto urls = 2;
    int32 page = 3;
    int32 limit = 4;
    int64 total = 5;
    int32 total_pages = 6;
    string query = 7;
}

// Delete URL Request/Response
message DeleteUrlRequest {
    string request_id = 1;
    string short_code = 2;
}

message DeleteUrlResponse {
    google.rpc.Status status = 1;
    bool success = 2;
}
