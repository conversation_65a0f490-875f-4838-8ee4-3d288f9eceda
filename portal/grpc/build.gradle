plugins {
    id "hnct.build"
    id "kotlin"
    id "com.google.protobuf"
}

dependencies {
    sourceSets {
        userImplementation main
        authImplementation main
        lsessionImplementation main
        classroomImplementation main
        configurationsImplementation main
        notificationImplementation main
        metadataDocImplementation main
        filestoreImplementation main
        betaImplementation main
        shorturlImplementation main
    }

    internal {
    }

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutineVs"

    // grpc dependencies
    implementation "com.google.protobuf:protobuf-java:$protobufVs"
    implementation "io.grpc:grpc-kotlin-stub:$grpcKotlinVs"
    implementation "io.grpc:grpc-stub:$grpcVs"
    implementation "io.grpc:grpc-protobuf:$grpcVs"
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:$protobufVs"
    }
    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:$grpcVs${platform}"
        }
        grpckt {
            artifact = "io.grpc:protoc-gen-grpc-kotlin:$grpcKotlinVs:jdk8@jar"
        }
    }
    generateProtoTasks {
        all().forEach {
            it.plugins {
                grpc{}
                grpckt{}
            }
        }
    }
}

version '1.0.0'
