package portal.user.pojo

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation
import org.bson.types.ObjectId
import proto.portal.user.UserMessage
import java.util.*

/**
 *
 * <AUTHOR>
 */
data class UserProfilePojo @BsonCreator constructor(
    @BsonId() @BsonRepresentation(BsonType.OBJECT_ID) val id: String = ObjectId().toHexString(),
    @BsonProperty("username") val username: String,
    @BsonProperty("email") val email: String,
    @BsonProperty("name") val name: String = "",
    @BsonProperty("avatarUrl") val avatarUrl: String,
    @BsonProperty("dateOfBirth") val dateOfBirth: Date? = null,
    @BsonProperty("gender") val gender: Gender = Gender.OTHER,
    @BsonProperty("address") val address: Address = Address(country = ""),
    @BsonProperty("phone") val phone: String = "",
    @BsonProperty("createdAt") var createdAt: Date? = null,
    @BsonProperty("updatedAt") var updatedAt: Date? = null,
    @BsonProperty("lastLogin") var lastLogin: Date? = null,
) {
    fun toProfileProto(): UserMessage.UserProfileProto {
        val builder = UserMessage.UserProfileProto.newBuilder()
            .setId(id)
            .setUsername(username)
            .setEmail(email)
            .setName(name)
            .setAvatarUrl(avatarUrl)
            .setGender(UserMessage.Gender.valueOf(gender.toString()))
            .setAddress(address.toAddressProto())
            .setPhone(phone)

        dateOfBirth?.let { builder.setDateOfBirth(dateOfBirth.time) }

        return builder.build()
    }
}
