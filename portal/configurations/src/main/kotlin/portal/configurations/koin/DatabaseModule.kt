package portal.configurations.koin

import com.mongodb.reactivestreams.client.MongoClients
import com.mongodb.reactivestreams.client.MongoCollection
import com.mongodb.reactivestreams.client.MongoDatabase
import org.bson.Document
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry
import org.bson.codecs.pojo.PojoCodecProvider
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.configurations.configs.DatabaseConfig

@Module
class DatabaseModule {

    @Singleton
    fun provideMongoDatabase(dbConf: DatabaseConfig): MongoDatabase {
        val mongoClient = MongoClients.create(dbConf.connectionString)
        val pojoCodecRegistry: CodecRegistry = CodecRegistries.fromRegistries(
            MongoClients.getDefaultCodecRegistry(),
            CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build())
        )
        return mongoClient.getDatabase(dbConf.dbName).withCodecRegistry(pojoCodecRegistry)
    }

    companion object {
        const val MONGO_COLLECTIONS = "MongoCollections"
    }

    @Singleton
    @Named(MONGO_COLLECTIONS)
    fun provideMongoCollections(dbConf: DatabaseConfig, database: MongoDatabase): Map<String, MongoCollection<Document>> {
        return dbConf.fieldMap.mapValues {
            database.getCollection(it.value)
        }
    }
}
