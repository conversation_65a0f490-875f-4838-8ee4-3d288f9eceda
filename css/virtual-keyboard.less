//
// Breakpoints
//
@xs: ~'(max-width: 414px)'; // <= iPhone 5
@sm: ~'(max-width: 744px)'; // <=744 -> phone
@md: ~'(max-width: 768px)'; // >= -> phone landscape
@lg: ~'(max-width: 1024px)'; // <=1024 -> tablet

@system:
  system-ui,
  -apple-system,
  BlinkMacSystemFont,
  'Segoe UI',
  '<PERSON><PERSON>',
  '<PERSON>xy<PERSON>',
  'Ubunt<PERSON>',
  'Cantarell',
  'Fira Sans',
  'Droid Sans',
  'Helvetica Neue',
  sans-serif;

@shadow2:
  0 3px 6px rgba(0, 0, 0, 0.16),
  0 3px 6px rgba(0, 0, 0, 0.23);
@shadow3:
  0 10px 20px rgba(0, 0, 0, 0.19),
  0 6px 6px rgba(0, 0, 0, 0.23);
@shadow4:
  0 14px 28px rgba(0, 0, 0, 0.25),
  0 10px 10px rgba(0, 0, 0, 0.22);
@shadow5:
  0 19px 38px rgba(0, 0, 0, 0.3),
  0 15px 12px rgba(0, 0, 0, 0.22);

//
// blue-50   #e2f0fd
// blue-100   #b6d9fb
// blue-200   #86c0f9
// blue-300   #56a6f6
// blue-400   #3193f4
// blue-500   #0d80f2
// blue-600   #0c75d8
// blue-700   #0c6abe
// blue-800   #0b5c9c
// blue-900   #094668

.ML__keyboard {
  // This value is calculated based on the actual height of layouts.
  --_keyboard-height: 0;

  --_keyboard-zindex: var(--keyboard-zindex, 105);

  --_accent-color: var(--keyboard-accent-color, #0c75d8); // blue-600
  --_background: var(--keyboard-background, #cacfd7);
  --_border: var(--keyboard-border, #ddd);

  --_padding-horizontal: var(--keyboard-padding-horizontal, 0px);
  --_padding-top: var(--keyboard-padding-top, 5px);
  --_padding-bottom: var(--keyboard-padding-bottom, 0px);

  --_row-padding-left: var(--keyboard-row-padding-left, 0px);
  --_row-padding-right: var(--keyboard-row-padding-right, 0px);

  --_toolbar-text: var(--keyboard-toolbar-text, #2c2e2f);
  --_toolbar-text-active: var(
    --keyboard-toolbar-text-active,
    var(--_accent-color)
  );
  --_toolbar-background: var(--keyboard-toolbar-background, transparent);
  --_toolbar-background-hover: var(--keyboard-toolbar-background-hover, #eee);
  --_toolbar-background-selected: var(
    --keyboard-toolbar-background-selected,
    transparent
  );
  --_toolbar-font-size: var(--keyboard-toolbar-font-size, '135%');

  --_horizontal-rule: var(--keyboard-horizontal-rule, 1px solid #fff);

  --_keycap-background: var(--keycap-background, #f0f0f0);
  --_keycap-background-hover: var(--keycap-background-hover, #f5f5f7);
  --_keycap-background-active: var(
    --keycap-background-active,
    var(--_accent-color)
  );
  --_keycap-background-pressed: var(
    --keycap-background-pressed,
    var(--_accent-color)
  );
  --_keycap-border: var(--keycap-border, #e5e6e9);
  --_keycap-border-bottom: var(--keycap-border-bottom, #8d8f92);
  --_keycap-text: var(--keycap-text, #000);
  --_keycap-text-active: var(--keycap-text-active, #fff);
  --_keycap-text-hover: var(--keycap-text-hover, var(--_keycap-text));
  --_keycap-text-pressed: var(--keycap-text-pressed, #fff);
  --_keycap-shift-text: var(--keycap-shift-text, var(--_accent-color));

  --_keycap-primary-background: var(
    --keycap-primary-background,
    var(--_accent-color)
  );
  --_keycap-primary-text: var(--keycap-primary-text, #ddd);
  --_keycap-primary-background-hover: var(
    --keycap-primary-background-hover,
    #0d80f2
  );

  --_keycap-secondary-background: var(--keycap-secondary-background, #a0a9b8);
  --_keycap-secondary-background-hover: var(
    --keycap-secondary-background-hover,
    #7d8795
  );
  --_keycap-secondary-text: var(--keycap-secondary-text, #060707);
  --_keycap-secondary-border: var(--keycap-secondary-border, #c5c9d0);
  --_keycap-secondary-border-bottom: var(
    --keycap-secondary-border-bottom,
    #989da6
  );

  --_keycap-height: var(--keycap-height, 60px);
  /* Keycap width (incl. margin) */
  --_keycap-max-width: var(--keycap-max-width, 100px);
  --_keycap-gap: var(--keycap-gap, 8px);

  --_keycap-font-size: var(--keycap-font-size, ~'clamp(16px, 4cqw, 24px)');
  --_keycap-small-font-size: var(
    --keycap-small-font-size,
    calc(var(--keycap-font-size) * 0.8)
  );
  --_keycap-extra-small-font-size: var(
    --keycap-extra-small-font-size,
    calc(var(--keycap-font-size) / 1.42)
  );

  --_variant-panel-background: var(--variant-panel-background, #f0f0f0);
  --_variant-keycap-text: var(--variant-keycap-text, var(--_keycap-text));
  --_variant-keycap-text-active: var(
    --variant-keycap-text-active,
    var(--_keycap-text-active)
  );
  --_variant-keycap-background-active: var(
    --variant-keycap-background-active,
    var(--_accent-color)
  );

  --_variant-keycap-length: var(--variant-keycap-length, 70px);
  --_variant-keycap-font-size: var(--variant-keycap-font-size, 30px);
  --_variant-keycap-aside-font-size: var(
    --variant-keycap-aside-font-size,
    12px
  );

  --_keycap-shift-font-size: var(--keycap-shift-font-size, 16px);
  --_keycap-shift-color: var(--keycap-shift-color, var(--_accent-color));

  --_box-placeholder-color: var(--box-placeholder-color, var(--_accent-color));
  --_box-placeholder-pressed-color: var(
    --box-placeholder-pressed-color,
    var(--keycap-text-pressed)
  );

  --_keycap-glyph-size: var(--keycap-glyph-size, 20px);
  --_keycap-glyph-size-lg: var(--keycap-glyph-size-lg, 24px);
  --_keycap-glyph-size-xl: var(--keycap-glyph-size-xl, 50px);
}

.is-math-mode .MLK__rows .if-text-mode,
.is-text-mode .MLK__rows .if-math-mode {
  display: none;
}

.if-can-undo,
.if-can-redo,
.if-can-copy,
.if-can-cut,
.if-can-paste {
  opacity: 0.4;
  pointer-events: none;
}

.can-undo .if-can-undo,
.can-redo .if-can-redo,
.can-copy .if-can-copy,
.can-cut .if-can-cut,
.can-paste .if-can-paste {
  opacity: 1;
  pointer-events: all;
}

// The keyboard can be attached to the body (most common case)
// or a custom container. When attached to the body, use a fixed position,
// otherwise a relative one.
// Account for the potential inset area at the bottom of the screen
body > .ML__keyboard {
  position: fixed;
  --_padding-bottom: calc(
    var(--keyboard-padding-bottom, 0px) + env(safe-area-inset-bottom, 0)
  );
}

// When *not* in a custom container, i.e. when attached to the body,
// add a small border and shadow at the top, and animate the transition
body > .ML__keyboard.is-visible > .MLK__backdrop {
  box-shadow: 0 -5px 6px rgb(0 0 0 / 8%);
  border-top: 1px solid var(--_border);
}

body > .ML__keyboard.backdrop-is-transparent.is-visible > .MLK__backdrop {
  box-shadow: none;
  border: none;
}

body > .ML__keyboard.is-visible.animate > .MLK__backdrop {
  // To make the keyboard visible, add the .is-visible class to ML__keyboard
  // This will slide in and fade in the keyboard
  transition: 0.28s cubic-bezier(0, 0, 0.2, 1); // Deceleration curve
  transition-property: transform, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1); // Acceleration curve
}

// Default layout for desktop (max-width: @lg)
.ML__keyboard {
  position: relative;
  overflow: hidden;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: var(--_keyboard-zindex);

  // "Reset" the keyboard
  box-sizing: border-box;
  outline: none;
  border: none;
  margin: 0;
  padding: 0;
  line-height: 1;
  overflow-wrap: unset;
  text-align: left;
  vertical-align: baseline;
  cursor: auto;
  white-space: pre;
  box-shadow: none;
  opacity: 1;
  transform: none;

  // The pointer events should pass through the "ML__keyboard" element
  // which is covering the entire screen, and be captured by the MLK__plate
  // otherwise this elements gets focus and the mathfield gets blured, causing
  // the virtual keyboard to be hidden.
  pointer-events: none;
}

// "Reset" the divs inside the keyboard
.ML__keyboard :where(div) {
  box-sizing: border-box;
  outline: none;
  border: none;
  margin: 0;
  padding: 0;
  line-height: 1;
  overflow-wrap: unset;
  text-align: left;
  vertical-align: baseline;
  cursor: auto;
  white-space: pre;
  box-shadow: none;
  transform: none;
}

// The backdrop displays the keyboard background and account for optional
// padding. It is placed inside a `.ML__keyboard` container
// so that it can be relatively positioned correctly when using a custom
// `virtualKeyboardContainer`
.MLK__backdrop {
  position: absolute;
  bottom: calc(-1 * var(--_keyboard-height));

  width: 100%;
  height: var(--_keyboard-height);
  box-sizing: border-box;

  padding-top: var(--_padding-top);
  padding-bottom: var(--_padding-bottom);
  padding-left: var(--_padding-horizontal);
  padding-right: var(--_padding-horizontal);

  opacity: 0;
  visibility: hidden;
  transform: translate(0, 0);

  background: var(--_background);
}

.backdrop-is-transparent .MLK__backdrop {
  background: transparent;
}

/* If a custom layout has a custom container/backdrop
  (backdrop-is-transparent), make sure to let pointer event go through. */
.backdrop-is-transparent .MLK__plate {
  background: transparent;
  pointer-events: none;
}

/* If a custom layout has a custom container/backdrop, make sure to 
   allow pointer events on it. */
.backdrop-is-transparent .MLK__layer > div > div {
  pointer-events: all;
}

.ML__keyboard.is-visible > .MLK__backdrop {
  transform: translate(0, calc(-1 * var(--_keyboard-height)));
  opacity: 1;
  visibility: visible;
}

.caps-lock-indicator {
  display: none;
  width: 8px;
  height: 8px;
  background: #0cbc0c;
  box-shadow:
    inset 0 0 4px 0 #13ca13,
    0 0 4px 0 #a9ef48;
  border-radius: 8px;
  right: 8px;
  top: 8px;
  position: absolute;
}
.ML__keyboard.is-caps-lock .caps-lock-indicator {
  display: block;
}
.ML__keyboard.is-caps-lock .shift {
  background: var(--_keycap-background-active);
  color: var(--_keycap-text-active);
}

// The keyboard "plate" is placed inside the `.MLK__backdrop`.
// It contains all the operable UI (toolbar, rows of keycaps)
.MLK__plate {
  position: absolute;
  top: var(--_padding-top);
  left: var(--_padding-horizontal);
  width: calc(100% - 2 * var(--_padding-horizontal));
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  container-type: inline-size;

  touch-action: none;
  -webkit-user-select: none;
  user-select: none;
  pointer-events: all;

  font-family: @system;
  font-size: 16px; /* Size of toolbar labels */
  font-weight: 400;
  text-shadow: none;
}

.ML__box-placeholder {
  color: var(--_box-placeholder-color);
}

.MLK__tex {
  font-family: KaTeX_Main, KaTeX_Math, 'Cambria Math', 'Asana Math', OpenSymbol,
    Symbola, STIX, Times, serif !important;
}

.MLK__tex-math {
  font-family: KaTeX_Math, KaTeX_Main, 'Cambria Math', 'Asana Math', OpenSymbol,
    Symbola, STIX, Times, serif !important;
  font-style: italic;
}

.MLK__layer {
  display: none;
  outline: none;
  &.is-visible {
    display: flex;
    flex-flow: column;
  }
}

/* Keyboard layouts are made or rows of keys... */
.MLK__rows {
  --_keycap-width: var(--keycap-width, ~'min(var(--_keycap-max-width), 10cqw)');

  display: flex;
  flex-flow: column;
  align-items: center;
  border-collapse: separate;
  clear: both;
  border: 0;
  margin: 0;
  margin-bottom: var(--_keycap-gap);
  gap: var(--_keycap-gap);

  /* If the styling include, e.g., some shadows, they will be
  cut off by the overflow. In that case, set the padding to 
  compensate. */
  padding-left: var(--_row-padding-left);
  padding-right: var(--_row-padding-right);
  overflow: visible;

  touch-action: none;
}

.MLK__rows > .MLK__row {
  display: flex;
  flex-flow: row;
  justify-content: center;
  width: 100%;
  gap: var(--_keycap-gap);
  margin: 0;
  padding: 0;

  .tex {
    font-family: KaTeX_Math, KaTeX_Main, 'Cambria Math', 'Asana Math',
      OpenSymbol, Symbola, STIX, Times, serif !important;
  }
  .tex-math {
    font-family: KaTeX_Math, 'Cambria Math', 'Asana Math', OpenSymbol, Symbola,
      STIX, Times, serif !important;
  }

  .big-op {
    font-size: calc(1.25 * var(--_keycap-font-size));
  }

  .small {
    font-size: var(--_keycap-small-font-size);
  }

  /* For the alignment of the text on some modifiers (e.g. shift) */
  .bottom {
    justify-content: flex-end;
  }
  .left {
    align-items: flex-start;
    padding-left: 12px;
  }

  .right {
    align-items: flex-end;
    padding-right: 12px;
  }

  .w0 {
    width: 0;
  }
  .w5 {
    width: calc(0.5 * var(--_keycap-width) - var(--_keycap-gap));
  }
  .w15 {
    width: calc(1.5 * var(--_keycap-width) - var(--_keycap-gap));
  }
  .w20 {
    width: calc(2 * var(--_keycap-width) - var(--_keycap-gap));
  }
  .w40 {
    width: calc(4 * var(--_keycap-width) - var(--_keycap-gap));
  }
  .w50 {
    width: calc(5 * var(--_keycap-width) - var(--_keycap-gap));
  }
  .MLK__keycap.w50 {
    font-size: 80%;
    padding-top: 10px;
    font-weight: 100;
  }
  /* Extra spacing between two adjacent keys */
  .separator {
    background: transparent;
    border: none;
    pointer-events: none;
  }
  .horizontal-rule {
    height: 6px;

    margin-top: 3px;
    margin-bottom: 0;
    width: 100%;
    border-radius: 0;
    border-top: var(--_horizontal-rule);
  }
  .ghost {
    background: var(--_toolbar-background);
    border: none;
    color: var(--_toolbar-text);
  }

  .ghost:hover {
    background: var(--_toolbar-background-hover);
  }

  .bigfnbutton {
    font-size: var(--_keycap-extra-small-font-size);
  }
  .shift,
  .action {
    color: var(--_keycap-secondary-text);
    background: var(--_keycap-secondary-background);
    border-color: var(--_keycap-secondary-border);
    border-bottom-color: var(--_keycap-secondary-border-bottom);
    line-height: 0.8;
    font-size: ~'min(1rem, var(--_keycap-small-font-size))';
    font-weight: 600;
    padding: 8px 12px 8px 12px;
    &:hover {
      background: var(--_keycap-secondary-background-hover);
    }
  }

  .action.primary {
    background: var(--_keycap-primary-background);
    color: var(--_keycap-primary-text);
  }
  .action.primary:hover {
    background: var(--_keycap-primary-background-hover); // blue-500
    color: var(--_keycap-primary-text);
  }

  .shift.selected,
  .action.selected {
    color: var(--_toolbar-text-active);
    &.is-pressed,
    &.is-active {
      color: white;
    }
  }

  // Use to display warning icons, with a SVG icon
  .warning {
    background: #cd0030;
    color: white;
    svg.svg-glyph {
      width: var(--_keycap-glyph-size-lg);
      height: var(--_keycap-glyph-size-lg);
      min-height: var(--_keycap-glyph-size-lg);
    }
  }
}

/** A regular keycap
 * Use the :where() pseudo-class to give it a very low specifity, 
 * so that it can be overriden by custom style.
 */
:where(.MLK__rows > .MLK__row div) {
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: space-evenly;

  width: calc(var(--_keycap-width) - var(--_keycap-gap));
  height: var(--_keycap-height);
  box-sizing: border-box;
  padding: 0;
  vertical-align: top;
  text-align: center;
  float: left;

  color: var(--_keycap-text);
  fill: currentColor;
  font-size: var(--_keycap-font-size);
  background: var(--_keycap-background);
  border: 1px solid var(--_keycap-border);
  border-bottom-color: var(--_keycap-border-bottom);
  border-radius: 6px;

  cursor: pointer;
  touch-action: none;

  &:hover {
    overflow: visible;
    background: var(--_keycap-background-hover);
    color: var(--_keycap-text-hover);
  }

  .ML__latex {
    pointer-events: none;
    touch-action: none;
  }

  /* Keys with a variants panel */
  // &[data-variants] {
  // }

  svg.svg-glyph {
    margin: 8px 0;
    width: var(--_keycap-glyph-size);
    height: var(--_keycap-glyph-size);
    min-height: var(--_keycap-glyph-size);
  }
  svg.svg-glyph-lg {
    margin: 8px 0;
    width: var(--_keycap-glyph-size-lg);
    height: var(--_keycap-glyph-size-lg);
    min-height: var(--_keycap-glyph-size-lg);
  }

  &.MLK__tex-math {
    font-size: 25px;
  }
  // &.tt {font-size: 30px;}
  &.is-pressed {
    // box-shadow: inset 0 0 1px 1px #8d8f92;
    background: var(--_keycap-background-pressed);
    color: var(--_keycap-text-pressed);
    --_box-placeholder-color: var(--_box-placeholder-pressed-color);
  }
  &.MLK__keycap.is-active,
  &.action.is-active,
  &.MLK__keycap.is-pressed,
  &.action.is-pressed {
    z-index: calc(var(--_keyboard-zindex) - 5);
    aside {
      display: none;
    }
    .MLK__shift {
      display: none;
    }
  }
  &.shift.is-pressed,
  &.MLK__keycap.is-pressed,
  &.action.is-pressed {
    background: var(--_keycap-background-pressed);
    color: var(--_keycap-text-pressed);
  }

  &.shift.is-active,
  &.MLK__keycap.is-active,
  &.action.is-active {
    background: var(--_keycap-background-active);
    color: var(--_keycap-text-active);
    --_box-placeholder-color: var(--_box-placeholder-pressed-color);
  }

  position: relative;
  overflow: hidden;
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  small {
    color: var(--_keycap-secondary-text);
  }
  aside {
    font-family: @system;
    font-size: 10px;
    line-height: 10px;
    color: var(--_keycap-secondary-text);
  }
}

/* Add an attribute 'data-tooltip' to display a tooltip on hover.
Note there are a different set of tooltip rules for the keyboard toggle
(it's in a different CSS tree) */
.ML__keyboard [data-tooltip] {
  position: relative;
  &::after {
    position: absolute;
    display: inline-table;
    content: attr(data-tooltip);

    top: inherit;
    bottom: 100%;
    width: max-content;
    max-width: 200px;
    padding: 8px 8px;
    background: #616161; // Grey 700
    color: #fff;
    text-align: center;
    z-index: 2;

    box-shadow:
      0 2px 2px 0 rgba(0, 0, 0, 0.14),
      0 1px 5px 0 rgba(0, 0, 0, 0.12),
      0 3px 1px -2px rgba(0, 0, 0, 0.2);
    border-radius: 2px;

    font-family: @system;
    font-weight: 400;
    font-size: 12px;

    transition: all 0.15s cubic-bezier(0.4, 0, 1, 1) 1s;
    opacity: 0;
    transform: scale(0.5);
  }
  &:hover {
    position: relative;
    &::after {
      opacity: 1;
      transform: scale(1);
    }
  }
}

.MLK__toolbar {
  align-self: center;
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  width: 100%;
  max-width: 996px;
  min-height: 32px;

  /* Icons for undo/redo, etc. */
  svg {
    height: 20px;
    width: 20px;
  }

  & > .left {
    position: relative;
    display: flex;
    justify-content: flex-start;
    flex-flow: row;
  }
  & > .right {
    display: flex;
    justify-content: flex-end;
    flex-flow: row;
  }
  & > div > div {
    /* "button" in the toolbar */
    display: flex;
    align-items: center;
    justify-content: center;

    color: var(--_toolbar-text);
    fill: currentColor;
    background: var(--_toolbar-background);
    font-size: var(--_toolbar-font-size);
    padding: 4px 15px;
    cursor: pointer;
    width: max-content;
    min-width: 42px;
    min-height: 34px;
    border: none;
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 8px;
    padding-top: 8px;
    margin-top: 0;
    margin-bottom: 4px;
    margin-left: 4px;
    margin-right: 4px;
    border-radius: 8px;
    box-shadow: none;
    border-bottom: 2px solid transparent;
    &:not(.disabled):not(.selected):hover {
      background: var(--_toolbar-background-hover);
    }
    &.disabled svg,
    &.disabled:hover svg,
    &.disabled.is-pressed svg {
      color: var(--_toolbar-text);
      opacity: 0.2;
    }

    &:hover,
    &:active,
    &.is-pressed,
    &.is-active {
      color: var(--_toolbar-text-active);
    }
    &.selected {
      color: var(--_toolbar-text-active);
      background: var(--_toolbar-background-selected);
      border-radius: 0;
      border-bottom-color: var(--_toolbar-text-active);
      padding-bottom: 4px;
      margin-bottom: 8px;
    }
  }
}

/* This is the element that displays variants on press+hold */
.MLK__variant-panel {
  visibility: hidden;

  position: fixed;
  display: flex;
  flex-flow: row wrap-reverse;
  justify-content: center;
  align-content: center;
  margin: 0;
  padding: 0;
  bottom: auto;
  top: 0;

  box-sizing: content-box;
  transform: none;
  z-index: calc(var(--_keyboard-zindex) + 1);

  touch-action: none;

  max-width: 350px;

  background: var(--_variant-panel-background);
  text-align: center;
  border-radius: 6px;
  padding: 6px;

  box-shadow: @shadow4;

  transition: none;

  &.is-visible {
    visibility: visible;
  }

  &.compact {
    --_variant-keycap-length: var(--variant-keycap-length, 50px);
    --_variant-keycap-font-size: var(--variant-keycap-font-size, 24px);
    --_variant-keycap-aside-font-size: var(
      --variant-keycap-aside-font-size,
      10px
    );
  }

  // margin: 0;
  // padding: 0;
  // display: flex;
  // flex-flow: row wrap-reverse;
  // justify-content: center;

  // Define li inside ul, as some external rules
  // might use `ul li` as a selector and override
  // this `li` otherwise
  .item {
    // Variant Keycaps
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    font-size: var(--_variant-keycap-font-size);

    height: var(--_variant-keycap-length);
    width: var(--_variant-keycap-length);

    @media (max-height: 412px) {
      --_variant-keycap-font-size: var(--variant-keycap-font-size, 24px);
      --_variant-keycap-length: var(--variant-keycap-length, 50px);
    }

    margin: 0;
    box-sizing: border-box;
    border-radius: 5px;
    border: 1px solid transparent; // #e5e6e9;

    background: transparent;
    pointer-events: all;
    cursor: pointer;

    // Rendered label inside a variant keycap
    .ML__latex {
      pointer-events: none;
    }

    color: var(--_variant-keycap-text);
    fill: currentColor;
    &:hover {
      color: var(--_keycap-text-hover);
    }
    &.is-active {
      background: var(--_variant-keycap-background-active);
      color: var(--_variant-keycap-text-active);
      --_box-placeholder-color: var(--_box-placeholder-pressed-color);
    }
    &.is-pressed {
      background: var(--_variant-keycap-background-pressed);
      color: var(--_variant-keycap-text-pressed);
      --_box-placeholder-color: var(--_box-placeholder-pressed-color);
    }
    &.small {
      font-size: var(--_keycap-small-font-size);
    }
    &.swatch-button {
      & > span {
        display: inline-block;
        margin: 6px;
        width: calc(100% - 12px);
        height: calc(100% - 12px);
        border-radius: 50%;
      }
      box-sizing: border-box;
      background: #fbfbfb;
      &:hover {
        background: #f0f0f0;
        & > span {
          border-radius: 2px;
        }
      }
    }
    &.box > div,
    &.box > span {
      border: 1px dashed rgba(0, 0, 0, 0.24);
    }
    // Use to display warning icons, with a SVG icon
    .warning {
      min-height: 60px;
      min-width: 60px;
      background: #cd0030;
      color: white;
      padding: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 5px;
      &.is-pressed,
      &.is-active {
        background: red;
      }
      svg.svg-glyph {
        width: var(--_keycap-glyph-size-xl);
        height: var(--_keycap-glyph-size-xl);
      }
    }
    aside {
      font-size: var(--_variant-keycap-aside-font-size);
      line-height: 12px;
      opacity: 0.78;
      padding-top: 2px;
      width: 100%;
      text-align: center;
      text-wrap: wrap;
    }
  }
}

.MLK__keycap {
  position: relative;
}

.MLK__shift {
  display: block;
  position: absolute;
  right: 4px;
  top: 4px;

  font-size: var(--_keycap-shift-font-size);
  color: var(--_keycap-shift-color);
}

.hide-shift .MLK__shift {
  display: none;
}

@media @xs {
  // Use a wider variant panel on small screens.
  // The variant panel is not inside the keyboard container, so this
  // is a media query, not a container query (there is no container)
  .MLK__variant-panel {
    max-width: 350px;
    --_variant-keycap-font-size: var(--variant-keycap-font-size, 24px);
    --_variant-keycap-length: var(--variant-keycap-length, 50px);
  }
}

/* @xs breakpoint: iPhone 5 */
@container @xs {
  .MLK__rows {
    --_keycap-gap: ~'max(var(--_keycap-gap, 2px), 2px)';
    --_keycap-height: ~'max(var(--_keycap-height), 42px)';
    --_keycap-width: var(
      --keycap-width,
      ~'min(min(var(--_keycap-max-width)), 10cqw), 62px)'
    );
  }

  // Smaller toolbar switcher labels
  .MLK__toolbar > div > div {
    font-size: 100%;
    margin-left: 2px;
    margin-right: 2px;
  }

  .MLK__rows .shift,
  .MLK__rows .action {
    font-size: 65%;
  }

  .MLK__rows .warning svg.svg-glyph {
    width: 14px;
    height: 14px;
    min-height: 14px;
  }
}

@container @sm {
  .MLK__rows {
    --_keycap-gap: ~'max(var(--keycap-gap, 2px), 2px)';
    --_keycap-height: ~'max(var(--keycap-height, 52px), 52px)';
    --_keycap-width: var(
      --keycap-width,
      ~'min(min(var(--_keycap-max-width), 10cqw), 62px)'
    );
  }

  .MLK__toolbar > div > div {
    // More compact toolbar switcher labels
    padding-left: 0;
    padding-right: 0;
  }
  .MLK__tooltip::after {
    padding: 8px 16px;
    font-size: 16px;
  }

  .MLK__rows > .MLK__row > div.fnbutton {
    font-size: 16px;
  }

  .MLK__rows > .MLK__row > div.bigfnbutton {
    font-size: calc(var(--_keycap-extra-small-font-size) / 1.55);
  }

  // Class for keycaps (not a tag)
  .MLK__rows > .MLK__row > div.small {
    font-size: 13px;
  }
  .MLK__rows > .MLK__row > div > aside {
    display: none;
  }
  .MLK__shift {
    display: none;
  }
}

/* Medium breakpoint: larger phones */
@container @md {
  .MLK__rows {
    --_keycap-height: ~'max(var(--keycap-height, 42px), 42px)';
  }

  .MLK__rows > .MLK__row > div > small {
    font-size: 14px;
  }
}

@media (max-height: 768px) {
  .MLK__rows {
    --_keycap-height: ~'max(var(--keycap-height, 42px), 42px)';
  }
  .MLK__rows > .MLK__row > div > small {
    font-size: 14px;
  }
}

@container (max-width: 1444px) {
  // Only show if there's extra room (up to four more columns)
  .MLK__rows .if-wide {
    display: none;
  }
}

//
// Dark theme
//
@media (prefers-color-scheme: dark) {
  .ML__keyboard {
    --_accent-color: var(--keyboard-accent-color, #0b5c9c); // blue-300
    --_background: var(--keyboard-background, #151515);
    --_border: var(--keyboard-border, transparent);
    --_toolbar-text: var(--keyboard-toolbar-text, #e3e4e8);

    --_toolbar-background-hover: var(
      --keyboard-toolbar-background-hover,
      #303030
    );
    --keyboard-toolbar-background-hover: #303030;

    --_horizontal-rule: var(--keyboard-horizontal-rule, 1px solid #303030);

    --_keycap-background: var(--keycap-background, #1f2022);
    --_keycap-background-hover: var(--keycap-background-hover, #2f3032);
    --_keycap-border: var(--_keycap-border, transparent);
    --_keycap-border-bottom: var(--_keycap-border-bottom, transparent);
    --_keycap-text: var(--keycap-text, #e3e4e8);

    --_keycap-secondary-background: var(--keycap-secondary-background, #3d4144);
    --_keycap-secondary-background-hover: var(
      --keycap-secondary-background-hover,
      #4d5154
    );
    --_keycap-secondary-text: var(--keycap-secondary-text, #e7ebee);
    --keycap-secondary-border: transparent;
    --keycap-secondary-border-bottom: transparent;
    --_keycap-secondary-border: var(--keycap-secondary-border, transparent);
    --_keycap-secondary-border-bottom: var(
      --keycap-secondary-border-bottom,
      transparent
    );

    --_variant-panel-background: var(--variant-panel-background, #303030);
    --_variant-keycap-text-active: var(--variant-keycap-text-active, #fff);
  }
}

/* Same as the media query, but with a class */
[theme='dark'] .ML__keyboard {
  --_accent-color: var(--keyboard-accent-color, #0b5c9c); // blue-300
  --_background: var(--keyboard-background, #151515);
  --_border: var(--keyboard-border, transparent);
  --_toolbar-text: var(--keyboard-toolbar-text, #e3e4e8);

  --_toolbar-background-hover: var(
    --keyboard-toolbar-background-hover,
    #303030
  );
  --keyboard-toolbar-background-hover: #303030;

  --_horizontal-rule: var(--keyboard-horizontal-rule, 1px solid #303030);

  --_keycap-background: var(--keycap-background, #1f2022);
  --_keycap-background-hover: var(--keycap-background-hover, #2f3032);
  --_keycap-border: var(--_keycap-border, transparent);
  --_keycap-border-bottom: var(--_keycap-border-bottom, transparent);
  --_keycap-text: var(--keycap-text, #e3e4e8);

  --_keycap-secondary-background: var(--keycap-secondary-background, #3d4144);
  --_keycap-secondary-background-hover: var(
    --keycap-secondary-background-hover,
    #4d5154
  );
  --_keycap-secondary-text: var(--keycap-secondary-text, #e7ebee);
  --keycap-secondary-border: transparent;
  --keycap-secondary-border-bottom: transparent;
  --_keycap-secondary-border: var(--keycap-secondary-border, transparent);
  --_keycap-secondary-border-bottom: var(
    --keycap-secondary-border-bottom,
    transparent
  );

  --_variant-panel-background: var(--variant-panel-background, #303030);
  --_variant-keycap-text-active: var(--variant-keycap-text-active, #fff);
}

[theme='light'] .ML__keyboard {
  --_accent-color: var(--keyboard-accent-color, #0c75d8); // blue-600
  --_background: var(--keyboard-background, #cacfd7);
  --_border: var(--keyboard-border, #ddd);

  --_toolbar-text: var(--keyboard-toolbar-text, #2c2e2f);
  --_toolbar-background: var(--keyboard-toolbar-background, transparent);
  --_toolbar-background-hover: var(--keyboard-toolbar-background-hover, #eee);
  --_toolbar-background-selected: var(
    --keyboard-toolbar-background-selected,
    transparent
  );

  --_horizontal-rule: var(--keyboard-horizontal-rule, 1px solid #fff);

  --_keycap-background: var(--keycap-background, white);
  --_keycap-background-hover: var(--keycap-background-hover, #f5f5f7);
  --_keycap-background-active: var(
    --keycap-background-active,
    var(--_accent-color)
  );
  --_keycap-background-pressed: var(
    --keycap-background-pressed,
    var(--_accent-color)
  );
  --_keycap-border: var(--_keycap-border, #e5e6e9);
  --_keycap-border-bottom: var(--_keycap-border-bottom, #8d8f92);
  --_keycap-text: var(--keycap-text, #000);
  --_keycap-text-active: var(--keycap-text-active, #fff);
  --_keycap-text-hover: var(--keycap-text-hover, var(--_keycap-text));
  --_keycap-text-pressed: var(--keycap-text-pressed, #fff);
  --_keycap-shift-text: var(--keycap-shift-text, var(--_accent-color));

  --_keycap-secondary-background: var(--keycap-secondary-background, #a0a9b8);
  --_keycap-secondary-background-hover: var(
    --keycap-secondary-background-hover,
    #7d8795
  );
  --_keycap-secondary-text: var(--keycap-secondary-text, #060707);
  --_keycap-secondary-border: var(--keycap-secondary-border, #c5c9d0);
  --_keycap-secondary-border-bottom: var(
    --keycap-secondary-border-bottom,
    #989da6
  );

  --_variant-panel-background: var(--variant-panel-background, #f0f0f0);
  --_variant-keycap-text: var(--variant-keycap-textvar, var(--_keycap-text));
  --_variant-keycap-text-active: var(
    --variant-keycap-text-active,
    var(--_keycap-text-active)
  );
  --_variant-keycap-background-active: var(
    --variant-keycap-background-active,
    var(--_accent-color)
  );
}
