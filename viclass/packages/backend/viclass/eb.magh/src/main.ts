import { NestFactory } from '@nestjs/core';
import type { NestExpressApplication } from '@nestjs/platform-express';
import { AppModule } from './app.module';

async function bootstrap() {
    const app = await NestFactory.create<NestExpressApplication>(AppModule, {
        bodyParser: true,
        rawBody: true,
        logger: ['debug'],
    });

    // use bodyParser to override the size limit of default NestJS request
    app.useBodyParser('json', { limit: '2mb' });
    app.useBodyParser('raw', { limit: '2mb' });
    app.useBodyParser('text', { limit: '2mb' });
    app.useBodyParser('urlencoded', { limit: '2mb', extended: true });

    await app.listen(8014);
}

bootstrap();
