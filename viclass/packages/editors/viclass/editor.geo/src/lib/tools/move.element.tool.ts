import { line, point, segment, vector } from '@flatten-js/core';
import {
    ErrorHandlerDecorator,
    PointerEventData,
    pointerTypeDyn,
    pointerTypePen,
    pointerTypePenMouse,
    ScreenPosition,
    START_PAN_THRESHOLD,
    UIPointerEventData,
} from '@viclass/editor.core';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { fromPromise } from 'rxjs/internal/observable/innerFrom';
import { map } from 'rxjs/operators';
import { syncEndPreviewModeCommand, syncPreviewCommands, syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryTool, GeometryToolType, GeoPointerEvent, GeoRenderElement } from '../geo.api';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    GeoElConstructionRequest,
    GeoPreviewElement,
    PreviewCircleShape,
    PreviewLine,
    PreviewSector,
    RenderVertex,
} from '../model';
import {
    MovementCirclePath,
    MovementLinePath,
    MovementLineSegmentPath,
    MovementSectorPath,
} from '../model/render.movement.path';
import { GeoDocCtrl } from '../objects';
import {
    addReconstructionHistoryItem,
    buildPreviewCircleShapeRenderProp,
    buildPreviewLineRenderProp,
    buildPreviewSectorRenderProp,
    defaultNonUIPointerEventHandler,
    handleIfPointerNotInError,
} from './tool.utils';

// Define the threshold constant
// pixels needs to be smaller or equal to start pan threshold of pan tool to give priority to this move element tool
const MOVE_THRESHOLD = START_PAN_THRESHOLD - 2;

/**
 * Tool for moving geometric elements with various movement constraints
 * Supports movement along:
 * - Line segments
 * - Infinite lines
 * - Circular arcs/sectors
 * - Full circles
 */
export class MoveElementTool extends GeometryTool<any> {
    readonly toolType: GeometryToolType = 'MoveElementTool';

    // Preview elements for visual feedback during movement
    private previewPoint?: RenderVertex; // Shows where element will move to
    private movementPath?: GeoPreviewElement; // Visual guide showing allowed movement path

    // RxJS subjects for handling movement events
    private src!: Subject<{ doc: GeoDocCtrl; pos: number[] }>;
    private dest!: Subscription;
    private moved: boolean = false; // Tracks if element has been moved from original position

    // ... existing properties ...
    private downPos?: ScreenPosition; // Store pointer down screen coordinates
    private thresholdPassed: boolean = false; // Track if threshold is met

    private pickedEl?: RenderVertex; // Currently selected element being moved

    // when first pointer down, we record the vertex that is picked together with its doc controller
    private downPosHitEl?: RenderVertex;
    private downPosHitCtrl?: GeoDocCtrl;

    /**
     * Initializes the Move Element Tool and registers all necessary mouse event handlers
     * @param editor The geometry editor instance
     * @param toolbar The geometry toolbar instance
     */
    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        // Register handlers for mouse events with and without button pressed
        this.registerPointerHandling(
            // move element tool only listening to trigger event globally
            // once element is picked, it should focus transiently to receive movement event.
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypePenMouse, global: true },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1, global: true },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1, global: true },

            // Listen globally for mouse move *with primary button down* to check threshold
            {
                event: 'pointermove',
                pressedButtons: 1, // Primary button pressed
                pointerTypes: ['mouse'],
                keys: ['nokey'],
                global: true, // Listen globally initially
            },

            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn, global: true },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen, global: true },

            // --- Local Handlers (Require Tool Focus - after threshold) ---
            { event: 'pointermove', pressedButtons: 1, pointerTypes: pointerTypePenMouse, keys: ['nokey'] },
            { event: 'pointerup', button: 0, pointerTypes: pointerTypePenMouse },

            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn },
            { event: 'pointerup', numTouch: 0, pointerTypes: pointerTypeDyn },
            { event: 'pointerup', pointerTypes: pointerTypePen }
        );
    }

    override onFocus() {
        console.log('Focus move element tool');
    }

    override onBlur() {
        console.log('Blur move element tool');
    }

    override resetState() {
        if (this.toolbar?.isToolActive(this.toolType)) {
            this.toolbar.blur(this.toolType, true); // Ensure transient focus is removed
        }
        this.internalResetState();
        super.resetState();
    }

    /**
     * Cleans up the tool's internal state variables and ends any active sequences
     */
    private internalResetState() {
        console.log('Remove internal state');
        this.moved = false; // Reset movement flag
        this.previewPoint = undefined; // Clear preview point
        this.pickedEl = undefined; // Clear selected element
        this.movementPath = undefined; // Clear movement path guide
        this.downPos = undefined;
        this.thresholdPassed = false; // Reset threshold flag
        this.endSequenceAndFlush(); // Clean up RxJS subscriptions
        this.editor.resetFilterElementFunc(); // Reset element filtering
    }

    /**
     * Starts a debounced sequence for processing move operations
     * Uses RxJS debounceTime to throttle rapid move events
     * @param dueTime The debounce delay in milliseconds
     */
    startSequence(dueTime: number) {
        this.src = new Subject();
        this.dest = this.src
            .pipe(
                debounceTime(dueTime), // Wait for pause in events
                map(e => fromPromise(this.doMove(e.doc, e.pos))) // Convert to promise
            )
            .subscribe();
    }

    /**
     * Adds a new position to the movement sequence
     * @param e Object containing document controller and position data
     */
    combine(e: { doc: GeoDocCtrl; pos: number[] }) {
        this.src?.next(e); // Push new position to subject
    }

    /**
     * Cleans up and unsubscribes from active sequences and subscriptions
     */
    endSequenceAndFlush() {
        // Clean up subject if it exists
        if (this.src) {
            this.src.complete();
            if (!this.src.closed) this.src.unsubscribe();
            delete this.src;
        }
        // Clean up subscription if it exists
        if (this.dest) {
            this.dest.unsubscribe();
            delete this.dest;
        }
    }

    /**
     * Routes mouse events to appropriate handlers based on event type
     * @param event The geometric mouse event to handle
     * @returns The original or modified event
     */
    override handlePointerEvent(event: PointerEventData<any>): PointerEventData<any> {
        if (this.toolbar.activeTool && this.toolbar.curTool != this.toolType) return event; // Ignore if another tool is active
        switch (event.eventType) {
            case 'pointerdown': {
                if (this.pickedEl) return event; // Ignore if element already picked
                this.onPointerDown(event);
                break;
            }
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    override handleNonUIPointerEvent = defaultNonUIPointerEventHandler(this);

    /**
     * Handles element selection and initializes movement path previews on mouse down
     * Sets up appropriate preview elements based on the type of movement path
     * @param event The mouse down event
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: UIPointerEventData<any>) {
        // if there are existing state because pointer up was not recorded
        if (this.downPosHitEl || this.downPosHitCtrl) this.resetState();

        const { ctrl, pos, hitEl } = this.posAndCtrl(event);

        if (!this.isValidVertex(hitEl)) {
            // If an element was previously picked but not moved, ensure blur is called
            if (this.pickedEl) {
                this.toolbar.blur(this.toolType, true); // Remove transient focus
                this.resetState(); // Reset internal state
            }
            return;
        }

        this.downPosHitEl = hitEl as RenderVertex;
        this.downPosHitCtrl = ctrl as GeoDocCtrl;

        // *** Store screen position and reset threshold ***
        this.downPos = { x: event.nativeEvent.clientX, y: event.nativeEvent.clientY };
        this.thresholdPassed = false;
        console.log('Pointerdown HIT!');

        // Prevent other tools from processing this down event further
        event.continue = false;
    }

    /**
     * Checks if the hit element is a valid vertex with a movement path
     */
    private isValidVertex(hitEl: GeoRenderElement | undefined): boolean {
        return hitEl?.type === 'RenderVertex' && !!(hitEl as RenderVertex).movementPath;
    }

    /**
     * Sets up initial state for movement operation
     */
    private setupInitialState(vertex: RenderVertex, ctrl: GeoDocCtrl) {
        this.editor.clearSelectedElInDoc(ctrl);
        this.pickedEl = vertex;
        this.startSequence(50);
    }

    /**
     * Creates preview visualization for movement path based on path type
     */
    private createMovementPathPreview(vertex: RenderVertex, ctrl: GeoDocCtrl) {
        const path = vertex.movementPath;

        switch (path.type) {
            case 'MovementCirclePath':
                this.createCirclePathPreview(path as MovementCirclePath, ctrl);
                break;
            case 'MovementSectorPath':
                this.createSectorPathPreview(path as MovementSectorPath, ctrl);
                break;
            case 'MovementLinePath':
                this.createLinePathPreview(path as MovementLinePath, ctrl);
                break;
            case 'MovementLineSegmentPath':
                this.createLineSegmentPathPreview(path as MovementLineSegmentPath, ctrl);
                break;
        }
    }

    /**
     * Creates circle path preview
     */
    private createCirclePathPreview(path: MovementCirclePath, ctrl: GeoDocCtrl) {
        this.movementPath = <PreviewCircleShape>{
            relIndex: -2000,
            elType: 'Circle',
            type: 'RenderCircleShape',
            name: undefined,
            centerPoint: path.pc,
            radius: path.radius,
            unselectable: true,
            valid: true,
            usable: true,
            renderProp: buildPreviewCircleShapeRenderProp(),
        };
        syncPreviewCommands(this.movementPath, ctrl);
    }

    /**
     * Creates sector path preview
     */
    private createSectorPathPreview(path: MovementSectorPath, ctrl: GeoDocCtrl) {
        this.movementPath = <PreviewSector>{
            relIndex: -2000,
            elType: 'CircularSector',
            type: 'RenderSector',
            name: undefined,
            centerPoint: path.pc,
            startPoint: path.ps,
            endPoint: path.pe,
            unselectable: true,
            valid: true,
            usable: true,
            renderProp: buildPreviewSectorRenderProp(),
        };
        syncPreviewCommands(this.movementPath, ctrl);
    }

    /**
     * Creates line path preview
     */
    private createLinePathPreview(path: MovementLinePath, ctrl: GeoDocCtrl) {
        this.movementPath = <PreviewLine>{
            relIndex: -2000,
            type: 'RenderLine',
            elType: 'LineVi',
            name: undefined,
            startPoint: path.root,
            vector: path.parallelVector,
            endPoint: undefined,
            usable: true,
            valid: true,
            unselectable: true,
            renderProp: buildPreviewLineRenderProp(),
        };
        syncPreviewCommands(this.movementPath, ctrl);
    }

    /**
     * Creates line segment path preview
     */
    private createLineSegmentPathPreview(path: MovementLineSegmentPath, ctrl: GeoDocCtrl) {
        this.movementPath = <PreviewLine>{
            relIndex: -2000,
            type: 'RenderLineSegment',
            elType: 'LineSegment',
            name: undefined,
            startPoint: path.p1,
            endPoint: path.p2,
            usable: true,
            valid: true,
            unselectable: true,
            renderProp: buildPreviewLineRenderProp(),
        };
        syncPreviewCommands(this.movementPath, ctrl);
    }

    /**
     * Creates preview point for current position
     */
    private createPositionPreview(vertex: RenderVertex, ctrl: GeoDocCtrl) {
        this.previewPoint = structuredClone(vertex);
        this.previewPoint.relIndex = -1000;
        this.previewPoint.name = undefined;
        this.previewPoint.renderProp.pointColor = '#991';
        this.previewPoint.unselectable = true;
        syncPreviewCommands(this.previewPoint, ctrl);
    }

    /**
     * Configures element filtering to only show picked element
     */
    private configureElementFiltering() {
        this.editor.filterElementFunc = el => el === this.pickedEl;
    }

    /**
     * Handles mouse movement with caching to prevent reflow
     * Delegates to mouseMoveCallback for actual processing
     * @param event The mouse move event
     */
    private onPointerMove(event: UIPointerEventData<any>) {
        // Only proceed if an element was picked on pointerdown
        if (!this.downPosHitEl && !this.pickedEl) return;
        // Check if the threshold has been passed
        if (!this.thresholdPassed) {
            if (!this.downPos) return; // Should not happen if pickedEl is set
            const currentPos = { x: event.nativeEvent.clientX, y: event.nativeEvent.clientY };
            const dx = Math.abs(currentPos.x - this.downPos.x);
            const dy = Math.abs(currentPos.y - this.downPos.y);

            // Check if movement exceeds threshold
            if (dx > MOVE_THRESHOLD || dy > MOVE_THRESHOLD) {
                this.thresholdPassed = true;
                // *** Gain transient focus NOW ***
                this.toolbar.focus(this.toolType, true);

                this.setupInitialState(this.downPosHitEl, this.downPosHitCtrl);
                this.createMovementPathPreview(this.downPosHitEl, this.downPosHitCtrl);
                this.createPositionPreview(this.downPosHitEl, this.downPosHitCtrl);
                this.configureElementFiltering();

                // Process this first move event immediately after gaining focus
                this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCB);
                this.downPosHitEl = undefined;

                event.continue = false; // Prevent further global handling for this event
                event.nativeEvent.preventDefault();
            }
            // If threshold not met yet, do nothing more with this move event globally
            // Allow event to continue for potential other global handlers (like pan)
            // but don't preventDefault unless we handle it.
        } else {
            // Threshold already passed, tool should be focused, handle as normal drag
            this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCB);
            event.continue = false; // Tool is focused and handling it
            event.nativeEvent.preventDefault();
        }
    }

    /**
     * Error handling wrapper for mouse move processing
     * @param event The mouse move event
     */
    pointerMoveCB = (event: GeoPointerEvent) => {
        return handleIfPointerNotInError(this, () => {
            this.processMouseMove(event);
        });
    };

    /**
     * Processes mouse movement events for the geometry editor.
     *
     * This method handles different interactions based on the current state:
     * 1. When no element is picked: Creates preview points when hovering over movable vertices
     * 2. When an element is picked: Calculates the projection of the mouse position onto
     *    the element's movement path and updates the preview point
     *
     * Supports multiple movement path types:
     * - MovementLineSegmentPath: Constrains movement to a line segment
     * - MovementLinePath: Constrains movement to a line
     * - MovementSectorPath: Constrains movement to a sector between two angles
     * - MovementCirclePath: Constrains movement to a circle
     *
     * Updates preview visualization and synchronizes commands with the controller.
     *
     * @param event - The geometric mouse event containing position and state information
     * @returns The hit element or the element being processed
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processMouseMove(event: GeoPointerEvent): GeoRenderElement {
        const { ctrl, pos, hitEl } = this.posAndCtrl(event);

        if (!this.pickedEl || !this.pickedEl.movementPath) return this.handleHoverPreview(hitEl, ctrl);

        this.moved = true;
        const p = this.calculateNewPosition(pos, this.pickedEl.movementPath);
        if (!p) return hitEl;

        this.updatePreview(p, ctrl);
        return hitEl;
    }

    private handleHoverPreview(hitEl: GeoRenderElement, ctrl: GeoDocCtrl): GeoRenderElement {
        if (hitEl instanceof RenderVertex) {
            const vertex = hitEl as RenderVertex;
            if (vertex.movementPath) {
                this.createPreviewPoint(vertex, ctrl);
                return hitEl;
            }
        }

        if (this.previewPoint) {
            syncEndPreviewModeCommand(ctrl);
            this.internalResetState();
        }
        return hitEl;
    }

    private createPreviewPoint(vertex: RenderVertex, ctrl: GeoDocCtrl) {
        this.previewPoint = structuredClone(vertex);
        this.previewPoint.relIndex = -1000;
        this.previewPoint.name = undefined;
        this.previewPoint.renderProp.pointColor = '#991';
        this.previewPoint.unselectable = true;
        syncPreviewCommands(this.previewPoint, ctrl);
    }

    private calculateNewPosition(pos: { x: number; y: number; z?: number }, movementPath: any): number[] | null {
        let p = [pos.x, pos.y, pos.z ?? 0];

        switch (movementPath.type) {
            case 'MovementLineSegmentPath':
                p = this.projectOntoLineSegment(pos, movementPath);
                break;
            case 'MovementLinePath':
                p = this.projectOntoLine(pos, movementPath);
                break;
            case 'MovementSectorPath':
                p = this.projectOntoSector(pos, movementPath);
                break;
            case 'MovementCirclePath':
                p = this.projectOntoCircle(pos, movementPath);
                break;
            default:
                break;
        }

        return p;
    }

    private projectOntoLineSegment(pos: { x: number; y: number }, path: MovementLineSegmentPath): number[] | null {
        const ps = point(path.p1[0], path.p1[1]);
        const pe = point(path.p2[0], path.p2[1]);
        const ln = line(ps, pe);
        const lsgm = segment(ps, pe);
        const projection = point(pos.x, pos.y).projectionOn(ln);
        if (!lsgm.contains(projection)) return null;
        return [projection.x, projection.y];
    }

    private projectOntoLine(pos: { x: number; y: number }, path: MovementLinePath): number[] {
        const v = vector(path.parallelVector[1], -path.parallelVector[0]);
        const ln = line(point(path.root[0], path.root[1]), v);
        const projection = point(pos.x, pos.y).projectionOn(ln);
        return [projection.x, projection.y];
    }

    private projectOntoSector(pos: { x: number; y: number }, path: MovementSectorPath): number[] | null {
        const pc = point(path.pc[0], path.pc[1]);
        const ps = point(path.ps[0], path.ps[1]);
        const pe = point(path.pe[0], path.pe[1]);
        const radius = pc.distanceTo(ps)[0];
        const pN = point(pos.x, pos.y);
        const vN = vector(pc, pN);
        const pc0 = point(pc.x + radius, pc.y);
        const vc0 = vector(pc, pc0);
        const an = vc0.angleTo(vN);
        const vcs = vector(pc, ps);
        const vce = vector(pc, pe);
        const as = vc0.angleTo(vcs);
        const ae = vc0.angleTo(vce);
        if (as > an || an > ae) return null;
        const pn = pc0.rotate(an, pc);
        return [pn.x, pn.y, 0.0];
    }

    private projectOntoCircle(pos: { x: number; y: number }, path: MovementCirclePath): number[] {
        const pc = point(path.pc[0], path.pc[1]);
        const pc0 = point(pc.x + path.radius, pc.y);
        const vc0 = vector(pc, pc0);
        const pN = point(pos.x, pos.y);
        const vN = vector(pc, pN);
        const an = vc0.angleTo(vN);
        const pn = pc0.rotate(an, pc);
        return [pn.x, pn.y, 0.0];
    }

    private updatePreview(p: number[], ctrl: GeoDocCtrl) {
        this.previewPoint.coords = structuredClone(p);
        syncPreviewCommands(this.previewPoint, ctrl);
        this.combine({ doc: ctrl, pos: p });
    }

    /**
     * Executes the actual element movement operation
     * Updates the element position and refreshes preview visualizations
     * @param ctrl The document controller
     * @param pos The new position coordinates
     */
    private async doMove(ctrl: GeoDocCtrl, pos: number[]) {
        if (!this.moved || !this.pickedEl) return;
        // Move element to new position
        const m = await this.editor.geoGateway.moveElement(ctrl.state.globalId, this.pickedEl.relIndex, pos);
        // Update rendering
        await syncRenderCommands(m.render, ctrl);
        if (this.movementPath) syncPreviewCommands(this.movementPath, ctrl);
        if (this.previewPoint) syncPreviewCommands(this.previewPoint, ctrl);
    }

    /**
     * Handles mouse up events to complete the movement operation
     * Reconstructs the element at its new position and adds to history
     * Selects the modified element after movement is complete
     * @param event The mouse up event
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: UIPointerEventData<any>) {
        const { ctrl, pos, hitEl } = this.posAndCtrl(event);
        let blurred = false; // Track if blur was called

        console.log('Move element tool process pointer up event');
        if (this.pickedEl) {
            const idx = this.pickedEl.relIndex;

            // Remove transient focus since the interaction is ending
            this.toolbar.blur(this.toolType, true);
            blurred = true; // Mark as blurred

            if (this.moved) {
                if (this.pickedEl.movementPath) {
                    const p = this.calculateNewPosition(pos, this.pickedEl.movementPath);
                    if (p) {
                        const [x, y, z] = p;
                        pos.x = x;
                        pos.y = y;
                        pos.z = z;
                    }
                }

                // Clean up movement state
                this.internalResetState();

                // Move element to final position
                const m = await this.editor.geoGateway.moveElement(ctrl.state.globalId, idx, [
                    pos.x,
                    pos.y,
                    pos.z ?? 0,
                ]);
                const newCt = m.newConstruction;

                // Create reconstruction request
                const req = new GeoElConstructionRequest(
                    newCt.ctId,
                    newCt.elType,
                    newCt.cgName,
                    newCt.name,
                    newCt.paramSpecs
                );

                // Reconstruct element at new position
                const c = await this.editor.geoGateway.reconstruct(ctrl.state.globalId, newCt.ctIdx, req);
                const oldCt = c.oldConstruction;

                // Add to history
                addReconstructionHistoryItem(
                    ctrl,
                    newCt.ctIdx,
                    newCt.ctId,
                    newCt.elType,
                    newCt.cgName,
                    newCt.name,
                    oldCt.paramSpecs,
                    newCt.paramSpecs
                );

                // Update rendering and select new element
                await syncRenderCommands(c.render, ctrl);
                this.resetState();
                const hitCtx = this.editor.checkHitInternal(ctrl.layers[0], event);
                this.editor.selectElement(hitCtx);
                return;
            } else {
                // Clean up preview elements if no movement occurred
                if (this.movementPath || this.previewPoint) {
                    syncEndPreviewModeCommand(ctrl);
                }

                this.resetState();
            }
        }

        if (!blurred && this.toolbar.isToolActive(this.toolType)) {
            // Ensure blur if pointerup happens while tool is somehow still focused without a picked element
            this.toolbar.blur(this.toolType, true);
        }
    }
}
