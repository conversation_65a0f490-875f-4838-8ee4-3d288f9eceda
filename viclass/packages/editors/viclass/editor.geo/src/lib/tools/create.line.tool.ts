import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { syncPreviewCommands, syncRenderCommands } from '../cmd';
import { PotentialSelectionDelegator } from '../delegators/potential.selection.delegator';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    GeoRenderElement,
    PreviewLine,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { constructExec, GeometryTool } from './geo.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    buildPreviewLineRenderProp,
    defaultNonUIPointerEventHandler,
    getPointAndVertex,
    handleIfPointerNotInError,
    pickPointName,
} from './tool.utils';

export class CreateLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateLineTool';

    private points: RenderVertex[] = [];
    private isPointerDown = false;
    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateLineTool> = new PotentialSelectionDelegator(
        this
    );

    protected override filterElementFunc = (el: GeoRenderElement) =>
        el.type == 'RenderVertex' && this.points.filter(p => p.relIndex == el.relIndex).length < 1;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/line preview
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.isPointerDown = false;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    override handleNonUIPointerEvent = defaultNonUIPointerEventHandler(this);

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer down is already set
        const { ctrl, coords, vertex } = getPointAndVertex(this, event);
        this.points.push(vertex);

        if (this.points.length == 1) {
            this.syncPreviewLine(ctrl, coords, coords);
            this.started = true;
        }

        this.isPointerDown = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event) || !this.points.length) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;

        this.potentialSelectionDelegator.clearPotential();

        if (this.points.length < 2) return;
        const { ctrl } = this.posAndCtrl(event);

        // submit construction
        const constructionPoints: GeoElConstructionRequest[] = [];
        const inputPointNames = (
            await this.requestElementNames(ctrl, [
                {
                    objName: 'Đường Thẳng',
                    originElement: this.points,
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            this.resetState();
            return;
        }

        for (let i = 0; i < this.points.length; i++) {
            const p = this.points[i];
            if (!p.name) {
                p.name = inputPointNames[i];
                const constructionPoint = buildPointConstruction(p.name, {
                    x: p.coords[0],
                    y: p.coords[1],
                });
                constructionPoints.push(constructionPoint);
            }
        }

        const lineName = `${this.points[0].name}${this.points[1].name}`;
        const constructionLine = this.buildLineConstruction(lineName);

        this.resetState();

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo đường thẳng',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(ctrl.state.globalId, [
                        ...constructionPoints.map(
                            c =>
                                <ConstructionRequest>{
                                    construction: c,
                                }
                        ),
                        {
                            construction: constructionLine,
                        },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (!this.points.length) return;
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        if (!this.points.length) return;
        const { ctrl, vertex, coords } = getPointAndVertex(this, event);
        if (this.isPointerDown) {
            if (!this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;

            const lastIdx = this.points.length - 1;
            this.points[lastIdx] = vertex;
            const [startP, endP] = [...this.points];
            this.syncPreviewLine(ctrl, startP.coords, endP?.coords);
        } else {
            const v1 = this.points[0].coords;
            this.syncPreviewLine(ctrl, v1, coords);
        }
    }

    private buildLineConstruction(name: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineVi/LineEC', 'LineVi', 'ByPointsName');
        construction.name = name;
        construction.paramSpecs = [];

        return construction;
    }

    private syncPreviewLine(ctrl: GeoDocCtrl, startV: number[], endV?: number[]) {
        const line: PreviewLine = {
            relIndex: -20,
            type: 'RenderLine',
            elType: 'LineVi',
            name: '',
            renderProp: buildPreviewLineRenderProp(),
            startPoint: startV,
            endPoint: endV ?? startV,
            usable: true,
            valid: true,
        };
        return syncPreviewCommands(line, ctrl);
    }
}
