import Flatten from '@flatten-js/core';
import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    InvalidUserInputErr,
    MouseEventData,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { Subject } from 'rxjs';
import { syncEndPreviewModeCommand, syncPreviewCommands, syncRenderCommands } from '../cmd';
import { PotentialSelectionDelegator } from '../delegators/potential.selection.delegator';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    ConstructionRequest,
    GeoElConstructionRequest,
    GeoRenderElement,
    PreviewPolygon,
    RegularPolygonToolState,
    RenderLineSegment,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { nthDirectionByLine } from '../nth.direction';
import { GeoDocCtrl } from '../objects';
import { constructExec, GeometryTool } from './geo.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    buildPreviewPointRenderProp,
    buildPreviewPolygonRenderProp,
    defaultNonUIPointerEventHandler,
    getFocusDocCtrl,
    getPointAndVertex,
    handleIfPointerNotInError,
    pickPointName,
} from './tool.utils';

import point = Flatten.point;
import circle = Flatten.circle;
import Point = Flatten.Point;

const EDGE_MIN = 3;
const EDGE_MAX = 8;

/**
 * Check if the number of edges is valid (an integer within the allowed range)
 */
function isValidEdge(noEdge: number) {
    return Number.isSafeInteger(noEdge) && noEdge >= EDGE_MIN && noEdge <= EDGE_MAX;
}

/**
 * Clamp the number of edges of the regular polygon within [3, 8] and round to the nearest integer.
 */
function clampEdge(noEdge: number) {
    return Math.min(Math.max(Math.round(noEdge) || EDGE_MIN, EDGE_MIN), EDGE_MAX);
}

/**
 * Tool for creating regular polygons. Allows users to draw a regular polygon by clicking on the canvas
 * and change the number of edges by scrolling the mouse wheel while holding Ctrl.
 */
export class CreateRegularPolygonTool extends GeometryTool<RegularPolygonToolState> {
    readonly toolType: GeometryToolType = 'CreateRegularPolygonTool';

    // List of selected points (center point, second point, ...)
    private points: RenderVertex[] = [];
    // Variable to determine rotation direction (1 or -1)
    private nth: number = 1;
    // relIndex used to assign to temporary preview points
    private relIndex: number = -100;
    // Flag to check pointer down state
    private isPointerDown = false;
    // Count the number of mouse clicks (1: select center, 2: select second point, 3: select direction)
    private clickCount = 0;

    // Object to assist in selecting potential elements when hovering the mouse
    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateRegularPolygonTool> =
        new PotentialSelectionDelegator(this);

    /**
     * pointClickNumber$: Subject emits the number of mouse clicks.
     * First click: draw renderLine; second click (if renderVertex): show input for number of edges.
     */
    readonly pointClickNumber$ = new Subject<number>();
    // Function to filter selectable elements on the canvas
    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return (
            (el.type == 'RenderVertex' &&
                (this.points[0]?.relIndex == el.relIndex ||
                    this.points.filter(p => p.relIndex == el.relIndex).length < 1)) ||
            el.type == 'RenderLineSegment'
        );
    };

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        // Register pointer events for the tool
        this.registerPointerHandling(
            // Start adding points
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // Confirm adding points
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },

            // Move point/preview
            { event: 'pointermove', pointerTypes: pointerTypeMouse, keys: ['nokey'] },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn },

            // Combine with Ctrl key
            { event: 'pointermove', button: 0, keys: ['ctrl'] }
        );
        // Register mouse wheel event to change the number of edges
        this.registerMouseHandling(
            {
                event: 'mousewheel',
                button: 0,
                pressedButton: 0,
                keys: ['ctrl'],
            },
            {
                event: 'wheel',
                button: 0,
                pressedButton: 0,
                keys: ['ctrl'],
            }
        );
        this.noEdge = this.toolState.noEdge;
    }

    // Check if the number of edges is valid
    get isValid(): boolean {
        return this.toolState.isValid;
    }

    // Get the current number of edges
    get noEdge(): number {
        return this.toolState.noEdge;
    }

    /**
     * Set a new number of edges if valid (within [3, 8]).
     * If the new value is less than the current value, end preview mode.
     * Update tool state and notify toolbar.
     *
     * !!! Must use this setter instead of assigning toolState.noEdge directly
     */
    set noEdge(noEdge: number) {
        this.toolState.isValid = isValidEdge(noEdge);

        if (this.isValid) {
            const docCtrl = getFocusDocCtrl(this.editor, this.toolbar.viewport?.id);
            if (docCtrl && noEdge < this.toolState.noEdge) syncEndPreviewModeCommand(docCtrl);
            this.toolState.noEdge = noEdge;
            if (docCtrl) this.previewPolygonFromPoints(docCtrl);
        }

        this.toolbar.update('CreateRegularPolygonTool', this.toolState);
    }

    /**
     * Reset the tool state to be ready to create a new polygon.
     * Clear the list of points, reset relIndex, nth, pointer down flag, click count.
     * Call resetState of the parent class.
     */
    override resetState() {
        this.points = [];
        this.relIndex = -100;
        this.nth = 1;
        this.isPointerDown = false;
        this.clickCount = 0;
        this.pointClickNumber$.next(0);
        super.resetState();
    }

    /**
     * Handle pointer events (mouse, pen, touch)
     */
    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    /**
     * Handle mouse events (mousewheel, wheel) to change the number of edges
     */
    override handleMouseEvent(event: MouseEventData<any>): MouseEventData<any> {
        switch (event.nativeEvent.type) {
            case 'mousewheel':
            case 'wheel': {
                if (this.started) {
                    this.onMouseWheel(event);
                    event.continue = false;
                }
                break;
            }
            default:
                break;
        }
        return event;
    }

    // Handle non-UI pointer events (default)
    override handleNonUIPointerEvent = defaultNonUIPointerEventHandler(this);

    /**
     * Handle mouse wheel event to increase/decrease the number of edges of the regular polygon.
     * Scroll up: increase edges, scroll down: decrease edges.
     */
    private async onMouseWheel(event: MouseEventData<any>) {
        const wheel = (event.nativeEvent as WheelEvent).deltaY < 0 ? 1 : -1;

        this.noEdge = clampEdge(this.noEdge + wheel);

        handleIfPointerNotInError(this, this.onPointerMove, event);
    }

    /**
     * Handle pointer down event to start creating a regular polygon.
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // Already pressed, ignore
        this.isPointerDown = true;
        this.clickCount++;

        // Third click: select polygon direction
        if (this.clickCount === 3) {
            await this.onFinalPointerDown(event);
        }
    }

    /**
     * Handle pointer up event to confirm the points of the regular polygon.
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return;
        this.isPointerDown = false;
        this.potentialSelectionDelegator.clearPotential();

        if (this.clickCount === 1) {
            // Confirm the first point (center)
            this.onFirstPointerUp(event);
        } else if (this.clickCount === 2) {
            // Confirm the second point
            this.onSecondPointerUp(event);
            this.pointClickNumber$.next(2);
        } else if (this.clickCount === 3) {
            // Confirm the direction and finish
            await this.finalizePolygon(event);
            this.pointClickNumber$.next(3);
        }
    }

    /**
     * Handle first pointer up to create the center point of the regular polygon.
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onFirstPointerUp(event: GeoPointerEvent) {
        if (!this.isValid) {
            this.noEdge = clampEdge(this.noEdge); // If the number of edges is invalid, reset to valid
        }

        const { ctrl, hitEl, vertex } = getPointAndVertex(this, event);
        if (hitEl?.type == 'RenderLineSegment') {
            // If selecting a line segment, use its two endpoints as the first two vertices
            const selectedLine = hitEl as RenderLineSegment;
            this.started = true;
            const point1 = ctrl.rendererCtrl.elementAt(selectedLine.startPointIdx) as RenderVertex;
            const point2 = ctrl.rendererCtrl.elementAt(selectedLine.endPointIdx) as RenderVertex;
            this.points = [point1, point2];
            this.clickCount = 2; // Skip selecting the second point
            this.editor.filterElementFunc = el => false;
            this.pointClickNumber$.next(1);
            this.previewPolygonFromPoints(ctrl);
            return;
        } else {
            // If selecting a point, save it as the center
            if (!this.points[0]) this.points[0] = vertex;

            this.editor.filterElementFunc = el =>
                el.type == 'RenderVertex' &&
                (this.points[0]?.relIndex == el.relIndex ||
                    this.points.filter(p => p.relIndex == el.relIndex).length < 1);
            syncPreviewCommands(this.points[0], ctrl);
        }

        this.started = true;
    }

    /**
     * Handle second pointer up to create the second vertex of the regular polygon.
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onSecondPointerUp(event: GeoPointerEvent) {
        const { ctrl, vertex } = getPointAndVertex(this, event);
        if (!this.points[1]) this.points[1] = vertex;
        this.editor.filterElementFunc = el => false;

        // Preview polygon with the first two points
        this.previewPolygonFromPoints(ctrl);
    }

    /**
     * Handle third pointer down to select the rotation direction of the regular polygon.
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onFinalPointerDown(event: GeoPointerEvent) {
        const { ctrl, pos } = this.posAndCtrl(event);

        // Update rotation direction (nth) based on pointer position
        const v = [pos.x, pos.y, 0.0];
        const v1 = this.points[0].coords;
        const v2 = this.points[1].coords;

        this.nth = nthDirectionByLine([v2[0] - v1[0], v2[1] - v1[1]], v1, v);

        // Preview polygon with new direction
        this.previewPolygonFromPoints(ctrl);
    }

    /**
     * Finalize the creation of the regular polygon based on the confirmed points and direction.
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async finalizePolygon(event: GeoPointerEvent) {
        if (!this.isValid) {
            this.resetState();
            throw new InvalidUserInputErr('Invalid number of edges');
        }

        const { ctrl } = this.posAndCtrl(event);

        let constructionPoints: GeoElConstructionRequest[] = [];
        let constructionPolygon: GeoElConstructionRequest;

        // Calculate the remaining vertices of the regular polygon
        const points = this.calculateRemainingVertices();

        // Create RenderVertex objects for the remaining vertices (preview)
        const vertexes = points.map(
            coords =>
                <RenderVertex>{
                    relIndex: this.relIndex--,
                    type: 'RenderVertex',
                    renderProp: buildPreviewPointRenderProp(),
                    name: undefined,
                    coords: coords,
                    usable: true,
                    valid: true,
                }
        );

        // Request input for point names (if needed)
        const inputPointNames = (
            await this.requestElementNames(ctrl, [
                {
                    objName: 'Regular Polygon',
                    originElement: this.points.concat(...vertexes),
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames) {
            this.resetState();
            return;
        }

        // Create construction points if the point does not have a name
        for (let i = 0; i < this.points.length; i++) {
            const p = this.points[i];
            if (!p.name) {
                p.name = inputPointNames[i];
                const constructionPoint = buildPointConstruction(p.name, {
                    x: p.coords[0],
                    y: p.coords[1],
                });
                constructionPoints.push(constructionPoint);
            }
        }

        const polygonName = inputPointNames.join('');
        if (constructionPoints.length < 2) {
            // If there are enough points, create the polygon from the line segment
            const lineName = this.points.map(p => p.name).join('');
            constructionPolygon = this.buildPolygonFromLineSegmentConstruction(
                polygonName,
                lineName,
                this.noEdge,
                this.nth
            );
        } else {
            // If not enough points, create the polygon from two positions
            constructionPolygon = this.buildPolygonFromTwoPositionConstruction(
                polygonName,
                this.points[0].coords,
                this.points[1].coords,
                this.noEdge,
                this.nth
            );
            constructionPoints = [];
        }

        this.resetState();

        // Execute the command to create the regular polygon and sync render, history
        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Creating regular polygon',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(ctrl.state.globalId, [
                        ...constructionPoints.map(
                            c =>
                                <ConstructionRequest>{
                                    construction: c,
                                }
                        ),
                        {
                            construction: constructionPolygon,
                        },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
    }

    /**
     * Calculate the remaining vertices of the regular polygon based on the first two points.
     * Returns an array of coordinates of the remaining vertices.
     */
    private calculateRemainingVertices(): number[][] {
        if (this.points.length < 2) return [];

        const points: number[][] = [];
        const v1 = this.points[0].coords;
        const v2 = this.points[1].coords;

        const p1 = point(v1[0], v1[1]);
        const p2 = point(v2[0], v2[1]);
        const angle = (2 * Math.PI) / this.noEdge;
        const size = p1.distanceTo(p2)[0];
        const r = size / 2 / Math.sin(angle / 2);
        const c1 = circle(p1, r);
        const c2 = circle(p2, r);
        const c1_x_c2 = c1.intersect(c2);

        if (c1_x_c2.length === 0) return []; // No intersection, cannot create polygon

        const pC = c1_x_c2[this.nth == 1 ? 1 : 0];

        // Function to create a new point by rotating around center pC
        const buildPoint2 = (p0: Point, i: number): number[] => {
            const p = p0.rotate(angle * i, pC);
            return [p.x, p.y, 0.0];
        };

        for (let i = 1; i < this.noEdge - 1; i += 1) {
            points.push(buildPoint2(p2, this.nth == 1 ? i : -i));
        }

        return points;
    }

    /**
     * Show a preview of the regular polygon based on the current points and calculated vertices.
     */
    private previewPolygonFromPoints(ctrl: GeoDocCtrl) {
        if (this.points.length < 2) return;

        const points = this.calculateRemainingVertices();

        const polygon: PreviewPolygon = {
            relIndex: -100,
            name: '',
            type: 'RenderPolygon',
            elType: 'Rectangle',
            faces: [...this.points.map(p => p.coords), ...points],
            renderProp: buildPreviewPolygonRenderProp(),
            usable: true,
            valid: true,
        };

        syncPreviewCommands(polygon, ctrl);
    }

    /**
     * Handle pointer move event (pointermove)
     */
    private onPointerMove(event: GeoPointerEvent) {
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    /**
     * Callback to handle pointermove for the tool, used to preview the regular polygon.
     * This function is registered with the editor's pointermove handler.
     */
    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    /**
     * Handle logic when moving the mouse to preview the regular polygon.
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        const { ctrl, pos, hitEl, vertex } = getPointAndVertex(this, event);
        let v = [pos.x, pos.y, 0.0];

        if (hitEl?.type === 'RenderVertex') {
            // If hovering over a point, use its coordinates
            const el = hitEl as RenderVertex;
            v = el.coords;
        }

        if (this.isPointerDown) {
            if (!this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;
            // Dragging the mouse
            if (this.clickCount === 1) {
                // Drag the first point (center)
                if (vertex) this.points[0] = vertex;
                else this.points[0] = null;

                if (this.points[0]) this.previewPoint(ctrl, this.points[0]);
                // Preview the first point
            } else if (this.clickCount === 2 || this.points.length == 1) {
                // Drag the second point
                if (vertex) this.points[1] = vertex;

                // Preview polygon with the new second point
                this.previewPolygonFromPoints(ctrl);
            } else if (this.clickCount === 3) {
                // Select polygon direction
                const v1 = this.points[0].coords;
                const v2 = this.points[1].coords;
                this.nth = nthDirectionByLine([v2[0] - v1[0], v2[1] - v1[1]], v1, v);

                // Preview polygon with new direction
                this.previewPolygonFromPoints(ctrl);
            }
        } else if (this.points.length > 0) {
            // Show preview based on current state
            if (this.points.length === 1) {
                // Preview line segment from center to pointer position
                const polygon: PreviewPolygon = {
                    relIndex: -100,
                    name: '',
                    type: 'RenderPolygon',
                    elType: 'Rectangle',
                    faces: [this.points[0].coords, v],
                    renderProp: buildPreviewPolygonRenderProp(),
                    usable: true,
                    valid: true,
                };

                syncPreviewCommands(polygon, ctrl);
            } else if (this.points.length === 2) {
                // Preview polygon with potential direction
                const v1 = this.points[0].coords;
                const v2 = this.points[1].coords;
                this.nth = nthDirectionByLine([v2[0] - v1[0], v2[1] - v1[1]], v1, v);

                this.previewPolygonFromPoints(ctrl);
            }
        }
    }

    /**
     * Create a construction request for a regular polygon from two positions (two points).
     */
    private buildPolygonFromTwoPositionConstruction(
        polygonName: string,
        pos1: number[],
        pos2: number[],
        noEdge: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RegularPolygon/RegularPolygonEC',
            'RegularPolygon',
            'FromTwoPosition'
        );
        construction.name = polygonName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-3DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: pos1,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-3DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: pos2,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-NoEdge',
                params: {
                    value: {
                        type: 'singleValue',
                        value: noEdge,
                    },
                },
            },
            {
                indexInCG: 3,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    /**
     * Create a construction request for a regular polygon from a line segment (two named points).
     */
    private buildPolygonFromLineSegmentConstruction(
        polygonName: string,
        lineName: string,
        noEdge: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RegularPolygon/RegularPolygonEC',
            'RegularPolygon',
            'FromLineSegment'
        );
        construction.name = polygonName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aName',
                optional: false,
                tplStrLangId: 'tpl-FromLineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-NoEdge',
                params: {
                    value: {
                        type: 'singleValue',
                        value: noEdge,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    /**
     * Show a preview point (RenderVertex) when selecting or hovering.
     */
    private previewPoint(ctrl: GeoDocCtrl, vertex: RenderVertex) {
        const preview: RenderVertex = {
            relIndex: -10,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewPointRenderProp(),
            name: undefined,
            coords: vertex.coords,
            usable: true,
            valid: true,
        };
        syncPreviewCommands(preview, ctrl);
    }
}
