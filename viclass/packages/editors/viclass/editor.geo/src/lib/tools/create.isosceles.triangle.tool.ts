import Flatten from '@flatten-js/core';
import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { syncPreviewCommands, syncRenderCommands } from '../cmd';
import { PotentialSelectionDelegator } from '../delegators/potential.selection.delegator';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    GeoRenderElement,
    PreviewPolygon,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { nthDirectionByLine } from '../nth.direction';
import { GeoDocCtrl } from '../objects';
import { constructExec, GeometryTool } from './geo.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    buildPreviewPolygonRenderProp,
    createVector,
    defaultNonUIPointerEventHandler,
    getPointAndVertex,
    handleIfPointerNotInError,
    pickPointName,
} from './tool.utils';
import circle = Flatten.circle;
import point = Flatten.point;
import line = Flatten.line;

export class CreateIsoscelesTriangleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateIsoscelesTriangleTool';

    private points: RenderVertex[] = [];
    private midPerpendicular: Flatten.Line;
    private isPointerDown = false;
    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateIsoscelesTriangleTool> =
        new PotentialSelectionDelegator(this);

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return el.type == 'RenderVertex' && this.points.filter(p => p.relIndex == el.relIndex).length < 1;
    };

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/preview
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.midPerpendicular = undefined;
        this.isPointerDown = false;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    override handleNonUIPointerEvent = defaultNonUIPointerEventHandler(this);

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;

        if (this.points.length == 0) {
            await this.handleFirstPoint(event);
        } else if (this.points.length == 1) {
            await this.handleSecondPoint(event);
        } else if (this.points.length == 2) {
            await this.handleThirdPoint(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;
        this.potentialSelectionDelegator.clearPotential();

        if (this.points.length == 2) {
            this.editor.filterElementFunc = el => {
                return false;
            };
        }

        if (this.points.length == 3) {
            await this.finalizeTriangle(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleFirstPoint(event: GeoPointerEvent) {
        const { ctrl, vertex } = getPointAndVertex(this, event);
        this.points.push(vertex);

        const v1 = vertex.coords;
        this.previewTriangle(ctrl, [v1, v1]);
        this.started = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleSecondPoint(event: GeoPointerEvent) {
        const { ctrl, vertex } = getPointAndVertex(this, event);
        this.points.push(vertex);

        const v1 = this.points[0].coords;
        const v2 = vertex.coords;

        this.calculateMidPerpendicular();
        this.previewTriangle(ctrl, [v1, v2]);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleThirdPoint(event: GeoPointerEvent) {
        const { ctrl, pos } = this.posAndCtrl(event, true);
        const { vertex } = getPointAndVertex(this, event, -10, true);
        this.points.push(vertex);

        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];

        const p = point(pos.x, pos.y);
        const projection = p.projectionOn(this.midPerpendicular);
        const v3 = [projection.x, projection.y, 0.0];

        // Update the vertex coords to be on the perpendicular bisector
        this.points[2] = {
            ...this.points[2],
            coords: v3,
        };

        this.previewTriangle(ctrl, [v1, v2, v3]);
    }

    private calculateMidPerpendicular() {
        if (this.points.length < 2) return;

        const vertex1 = this.points[0];
        const vertex2 = this.points[1];
        const center1 = point(vertex1.coords[0], vertex1.coords[1]);
        const center2 = point(vertex2.coords[0], vertex2.coords[1]);
        const r = center1.distanceTo(center2)[0];
        const c1 = circle(center1, r);
        const c2 = circle(center2, r);
        const intersections = c1.intersect(c2);
        this.midPerpendicular = line(intersections[0], intersections[1]);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async finalizeTriangle(event: GeoPointerEvent) {
        const { ctrl, docGlobalId } = this.posAndCtrl(event);

        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
        const v3 = this.points[2].coords;

        const calculationVector = createVector(this.points[0], this.points[1]);
        const nth = nthDirectionByLine(calculationVector, v1, v3);

        // Calculate height for potential construction
        const l = line(point(v1[0], v1[1]), point(v2[0], v2[1]));
        const p3 = point(v3[0], v3[1]);
        const height = p3.distanceTo(l)[0];

        // submit construction
        const constructionPoints: GeoElConstructionRequest[] = [];
        const inputPointNames = (
            await this.requestElementNames(ctrl, [
                {
                    objName: 'Tam Giác',
                    originElement: this.points,
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            this.resetState();
            return;
        }

        for (let i = 0; i < this.points.length; i++) {
            const p = this.points[i];
            if (!p.name) {
                p.name = inputPointNames[i];
                const constructionPoint = buildPointConstruction(p.name, {
                    x: p.coords[0],
                    y: p.coords[1],
                });
                constructionPoints.push(constructionPoint);
            }
        }

        const lineName = `${this.points[0].name}${this.points[1].name}`;
        const pName = inputPointNames[2];
        const triangleName = `${lineName}${pName}`;

        let constructionTriangle: GeoElConstructionRequest;
        if (this.points.length === 3) {
            constructionTriangle = this.buildTriangleFromPointsConstruction(this.points.map(p => p.name));
        } else {
            constructionTriangle = this.buildTriangleConstruction(triangleName, lineName, height, nth);
        }

        this.resetState();

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo hình tam giác cân',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(docGlobalId, [
                        ...constructionPoints.map(
                            c =>
                                <ConstructionRequest>{
                                    construction: c,
                                }
                        ),
                        {
                            construction: constructionTriangle,
                        },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (this.points.length === 0 || this.points.length > 3) return;

        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async processPointerMove(event: GeoPointerEvent) {
        const stopSnap = this.points.length >= 2;
        const { ctrl, pos, vertex } = getPointAndVertex(this, event, -10, stopSnap);

        if (this.isPointerDown) {
            if (!this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;

            if (this.points.length > 0) {
                const lastIndex = this.points.length - 1;
                this.points[lastIndex] = vertex;

                if (this.points.length === 1) {
                    this.previewTriangle(ctrl, [vertex.coords, vertex.coords]);
                } else if (this.points.length === 2) {
                    this.calculateMidPerpendicular();
                    this.previewTriangle(ctrl, [this.points[0].coords, vertex.coords]);
                } else if (this.points.length === 3) {
                    // For the third point, project onto perpendicular bisector
                    const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
                    const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];

                    const p = point(pos.x, pos.y);
                    const projection = p.projectionOn(this.midPerpendicular);
                    const v3 = [projection.x, projection.y, 0.0];

                    // Update the point's coords with the projection
                    this.points[2] = {
                        ...this.points[2],
                        coords: v3,
                    };

                    this.previewTriangle(ctrl, [v1, v2, v3]);
                }
            }
        } else {
            // Preview for next point
            if (this.points.length === 1) {
                const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
                this.previewTriangle(ctrl, [v1, vertex.coords]);
            } else if (this.points.length === 2) {
                const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
                const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];

                const p = point(pos.x, pos.y);
                const projection = p.projectionOn(this.midPerpendicular);
                const v3 = [projection.x, projection.y, 0.0];

                this.previewTriangle(ctrl, [v1, v2, v3]);
            }
        }
    }

    private previewTriangle(ctrl: GeoDocCtrl, faces: number[][]) {
        const polygon: PreviewPolygon = {
            relIndex: -20,
            name: '',
            type: 'RenderPolygon',
            elType: 'IsoscelesTriangle',
            faces: faces,
            renderProp: buildPreviewPolygonRenderProp(),
            usable: true,
            valid: true,
        };

        syncPreviewCommands(polygon, ctrl);
    }

    private buildTriangleConstruction(
        triangleName: string,
        lineName: string,
        height: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'IsoscelesTriangle/IsoscelesTriangleEC',
            'IsoscelesTriangle',
            'BaseLineSegmentAndHeightValue'
        );
        construction.name = triangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-BaseSideIs',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-HeightValue',
                params: {
                    value: {
                        type: 'singleValue',
                        value: height,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private buildTriangleFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'IsoscelesTriangle/IsoscelesTriangleEC',
            'IsoscelesTriangle',
            'FromPoints'
        );
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aName',
                optional: false,
                tplStrLangId: 'tpl-AtPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: pointNames[2],
                    },
                },
            },
        ];

        return construction;
    }
}
