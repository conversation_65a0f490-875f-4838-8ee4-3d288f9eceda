import {
    BoardViewportManager,
    ChangeToolEventData,
    DefaultEventData,
    reliableSaveCmdMeta,
    SelectionEvent,
    SupportFeatureHistory,
    VEventListener,
    ViewportContentEvent,
} from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.geo';
import { Subject, Subscription } from 'rxjs';
import { sampleTime } from 'rxjs/operators';
import {
    convertDocDefaultElRenderPropsToProto,
    convertDocRenderPropToProto,
    UpdateDocDefaultElRenderPropsCmd,
    UpdateDocStateCmd,
    UpdateElementsPropCmd,
} from '../cmd';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { DocRenderPropHistoryItem, ElementPropHistoryItem } from '../history';
import {
    convertSettingPropertiesToProto,
    DefaultGeoRenderProp,
    DocRenderProp,
    GeoRenderElement,
    SettingPropertyType,
    UpdatePropToolState,
} from '../model';
import { GeometryToolType, GeoPointerEvent, GeoToolEventData } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { GeometryTool } from './geo.tool';
import { isValidIdx } from './tool.utils';

/**
 * Tool for updating properties of geometric elements and document render properties.
 * It listens to selection changes and updates its state accordingly.
 * It also handles changes from the UI and sends appropriate commands to the editor.
 * <AUTHOR>
 */
export class UpdatePropTool extends GeometryTool<UpdatePropToolState> {
    readonly toolType: GeometryToolType = 'UpdatePropTool';

    /**
     * Event listener for viewport content changes, primarily selection events.
     */
    readonly viewportContentEventListener: VEventListener<ViewportContentEvent>;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);

        // Initialize the viewport content event listener.
        this.viewportContentEventListener = new (class ViewportContentEventListener
            implements VEventListener<ViewportContentEvent>
        {
            // Subscriptions to various document and selection observables.
            private docRenderPropSub: Subscription;
            private docDefaultElRenderPropsSub: Subscription;
            private selectedElementsSub: Subscription;

            constructor(private tool: UpdatePropTool) {}

            onEvent(eventData: ViewportContentEvent): ViewportContentEvent | Promise<ViewportContentEvent> {
                // Ignore events from other viewports.
                if (this.tool.toolbar.viewport.id !== eventData.source.id) return eventData;

                // Extract selection event and document controller.
                const selectionEvent = eventData.state.originalEvent as SelectionEvent;
                const doc = selectionEvent.source.curDocSelection?.[0]?.doc;

                // If no document or document editor doesn't match, ignore.
                if (!doc || doc.editor !== this.tool.editor) return eventData;

                // Reset previous state before processing new event.
                this.resetState();

                // If a single document is selected and it's a GeoDocCtrl instance.
                if (selectionEvent.source.curDocSelection.length === 1 && doc instanceof GeoDocCtrl) {
                    const toolState = this.tool.toolState;

                    // Register listener for changes in renderer elements (e.g., sectors).
                    // register element change
                    doc.rendererCtrl.registerElementChange(this.tool.rendererElementChangeListener);

                    // update doc change
                    toolState.doc = doc;
                    toolState.docRenderProp = doc.state.docRenderProp;
                    toolState.selectedElements = doc.selectedElements;
                    toolState.notProcess = true;
                    toolbar.update('UpdatePropTool', toolState);

                    // listen to docRenderProp changes
                    this.docRenderPropSub = doc.docRenderProp$.subscribe((prop: DocRenderProp) => {
                        toolState.docRenderProp = prop;
                        toolState.notProcess = true;
                        toolbar.update('UpdatePropTool', toolState);
                    });

                    // listen to docDefaultElRenderProps changes
                    this.docDefaultElRenderPropsSub = doc.docDefaultElRenderProps$.subscribe(
                        (docDefaultElRenderProps: DefaultGeoRenderProp) => {
                            toolState.docDefaultElRenderProps = docDefaultElRenderProps;
                            toolState.notProcess = true;
                            toolbar.update('UpdatePropTool', toolState);
                        }
                    );

                    // listen to selectedElements changes
                    this.selectedElementsSub = doc.selectedElements$.subscribe(selectedElements => {
                        toolState.elementProps = {} as any;
                        toolState.selectedElements = selectedElements;
                        toolState.notProcess = true;
                        toolbar.update('UpdatePropTool', toolState);
                    });
                }

                return eventData;
            }

            /**
             * Cleans up subscriptions and listeners when the event listener is unregistered.
             */
            onUnregister() {
                this.resetState();
            }

            /**
             * Resets the tool's state and unsubscribes from all active subscriptions.
             */
            resetState() {
                this.resetToolState();
                this.selectedElementsSub?.unsubscribe();
                this.docRenderPropSub?.unsubscribe();
                this.docDefaultElRenderPropsSub?.unsubscribe();
                this.tool.toolState.doc?.rendererCtrl.unregisterElementChange(this.tool.rendererElementChangeListener);
            }

            /**
             * Resets the tool's UI state.
             */
            resetToolState() {
                this.tool.toolState.reset();
                this.tool.toolbar.update('UpdatePropTool', this.tool.toolState);
            }
        })(this);
    }

    /**
     * Event listener for changes in the renderer's elements.
     * This is used to update the tool's state when, for example, sectors are added or removed.
     */
    private readonly rendererElementChangeListener = new (class
        implements VEventListener<DefaultEventData<string, GeoRenderElement>>
    {
        constructor(private readonly tool: UpdatePropTool) {}

        /**
         * Handles element change events from the renderer.
         * @param eventData The event data containing information about the changed element.
         */
        onEvent(eventData: DefaultEventData<string, GeoRenderElement>): DefaultEventData<string, GeoRenderElement> {
            if (eventData.eventType === 'elementChange') {
                const toolState = this.tool.toolState;
                const rendererCtrl = toolState?.doc?.rendererCtrl;
                if (!rendererCtrl) return eventData;

                toolState.notProcess = true;
                this.tool.toolbar.update('UpdatePropTool', toolState);
            }

            return eventData;
        }
    })(this);

    /**
     * Processes changes in the tool's state, typically triggered by UI interactions.
     * This method determines what kind of property update is occurring (document or element)
     * and calls the appropriate handler.
     * @param event The tool event data containing the state changes.
     */
    protected override async processChangeToolEvent(event: GeoToolEventData): Promise<GeoToolEventData> {
        const changeEvent = event as ChangeToolEventData<GeometryToolBar, GeometryToolType>;
        const toolState = event.state as UpdatePropToolState;
        // If 'notProcess' is true, it means the state change was internal (e.g., from a subscription)
        // and should not trigger another update cycle.
        if (changeEvent.changes.has('notProcess') && toolState.notProcess === true) return event;

        // Handle update doc state
        if (changeEvent.changes?.has('docRenderProp')) {
            this.updateDocState(changeEvent.changes.get('docRenderProp').previousValue, toolState.docRenderProp);
        }
        if (changeEvent.changes?.has('docDefaultElRenderProps'))
            this.updateDocDefaultElRenderProps(toolState.docDefaultElRenderProps);

        // Handle update element props
        if (
            changeEvent.changes?.has('elementProps') &&
            typeof toolState.elementProps === 'object' &&
            Object.keys(toolState.elementProps).length
        ) {
            const props = {} as {
                [key in SettingPropertyType]: any;
            };
            for (const propName of Object.values(SettingPropertyType))
                if (propName in toolState.elementProps) props[propName] = toolState.elementProps[propName];
            this.updateElementState(props);
        }

        return event;
    }

    /**
     * Updates the properties of the selected geometric elements.
     * It collects the current properties of the selected elements for history purposes,
     * then creates and sends an `UpdateElementsPropCmd`.
     * @param props A dictionary of `SettingPropertyType` to their new values.
     */
    private async updateElementState(props: {
        // Make async to potentially await state fetching
        [key in SettingPropertyType]: any;
    }) {
        if (!props || !(typeof props === 'object') || !Object.keys(props).length) return;

        const doc = this.toolState.doc;
        if (!doc) return;

        const selectedEls = this.toolState.selectedElements;
        if (!selectedEls || !selectedEls?.length) return;

        const relIndexes = new Set<number>();

        for (const el of selectedEls) {
            // Collect the primary element's relIndex.
            relIndexes.add(el.relIndex);
            switch (el.renderProp.type) {
                case 'SectorShape':
                    if (
                        SettingPropertyType.showArcLabel in props ||
                        SettingPropertyType.arcLabelType in props ||
                        SettingPropertyType.arcLabelContent in props
                    )
                        if (isValidIdx(el.arcRelIdx))
                            // If arc-related properties are changing for a SectorShape,
                            // also include the arc's relIndex if it's valid.
                            relIndexes.add(el.arcRelIdx);

                    break;
            }
        }
        // --- History Integration START ---
        const beforeProps: {
            relIndex: number;
            props: {
                [key in SettingPropertyType]: any;
            };
        }[] = [];
        for (const el of selectedEls) {
            // Capture the state of properties *before* the change for the history item.
            const beforePropsItem = {} as {
                [key in SettingPropertyType]: any;
            };
            for (const propName of Object.keys(props)) {
                if (propName in el.renderProp) {
                    beforePropsItem[propName] = el.renderProp[propName];
                }
            }

            // Only add to history if some properties were actually present on the element.
            if (Object.keys(beforePropsItem).length > 0)
                beforeProps.push({
                    relIndex: el.relIndex,
                    props: beforePropsItem,
                });
        }

        // Create the command to update element properties.
        const meta = reliableSaveCmdMeta(
            this.toolbar.viewport,
            doc.state,
            doc.state.id,
            doc.state.id,
            CmdTypeProto.UPDATE_ELS_PROP
        );
        const cmd = new UpdateElementsPropCmd(meta);
        const relIndexList = Array.from(relIndexes);
        cmd.state.setRelIndexList(relIndexList);
        cmd.state.setElRenderProps(convertSettingPropertiesToProto(props));

        // Send the command to the editor's command channel.
        await this.editor.cmdChannel.receive(cmd);

        // Create and add a history item for this change.
        const historyItem = new ElementPropHistoryItem(this.editor as SupportFeatureHistory);
        historyItem.type = 'element-prop';
        historyItem.vm = this.toolbar.viewport as BoardViewportManager;
        historyItem.docId = doc.state.id;
        historyItem.beforeStates = beforeProps; // Captured before state
        historyItem.afterStates = {
            relIndexList: relIndexList,
            props, // State sent in the command
        }; // State sent in the command
        this.editor.addHistoryItem(historyItem);
        // --- History Integration END ---
    }

    /**
     * Initiates an update for the document's render properties.
     * This method pushes the new `docRenderProp` to an RxJS Subject,
     * which then debounces the updates.
     * @param docRenderProp The new document render properties.
     */
    updateDocState(oldDocRenderProp: DocRenderProp, docRenderProp: DocRenderProp) {
        if (!this.toolState.doc) return;

        const doc = this.toolState.doc;
        const meta = reliableSaveCmdMeta(
            this.toolbar.viewport,
            doc.state,
            doc.state.id,
            doc.state.id,
            CmdTypeProto.UPDATE_DOC_STATE,
            true
        );
        const cmd = new UpdateDocStateCmd(meta);

        // --- History Integration START ---
        cmd.setState(doc.state.globalId, convertDocRenderPropToProto(docRenderProp));

        // Add history item if the editor supports history
        // and if isSaveToHistory is not explicitly false
        if (this.toolState.isSaveToHistory !== false && 'addHistoryItem' in this.editor) {
            const historyItem = new DocRenderPropHistoryItem(this.editor as SupportFeatureHistory);
            historyItem.type = 'doc-render-prop'; // Define a type if needed
            historyItem.vm = this.toolbar.viewport as BoardViewportManager;
            historyItem.docId = doc.state.id;
            historyItem.beforeState = oldDocRenderProp;
            historyItem.afterState = docRenderProp; // Use the calculated afterState
            this.editor.addHistoryItem(historyItem);
        }
        // --- History Integration END ---

        this.editor.cmdChannel.receive(cmd);
    }

    /**
     * Updates the document's default element render properties.
     * @param docDefaultElRenderProps The new default element render properties.
     */
    private updateDocDefaultElRenderProps(docDefaultElRenderProps: DefaultGeoRenderProp) {
        const doc = this.toolState.doc;
        if (!doc) return;

        // cmd
        const meta = reliableSaveCmdMeta(
            this.toolbar.viewport,
            doc.state,
            doc.state.id,
            doc.state.id,
            CmdTypeProto.UPDATE_DEFAULT_EL_RENDER_PROPS
        );
        const cmd = new UpdateDocDefaultElRenderPropsCmd(meta);
        cmd.state.setDocDefaultElRenderProps(
            convertDocDefaultElRenderPropsToProto({
                ...this.toolState.docDefaultElRenderProps,
                ...docDefaultElRenderProps,
            } as DefaultGeoRenderProp)
        );
        this.editor.cmdChannel.receive(cmd);
    }

    /**
     * Handles pointer events. Currently, this tool does not directly interact with pointer events
     * for property updates; changes are typically driven by UI elements bound to the tool's state.
     * @param event The pointer event.
     */
    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        return event;
    }

    /**
     * Lifecycle hook called when the tool is attached to a viewport.
     * Registers the viewport content event listener and subscribes to document render property updates.
     */
    override onAttachViewport() {
        this.toolbar.viewport.registerContentEventListener(this.viewportContentEventListener);
    }

    /**
     * Lifecycle hook called when the tool is detached from a viewport.
     * Unregisters event listeners and unsubscribes from observables.
     */
    override onDetachViewport() {
        this.toolbar.viewport.unregisterContentEventListener(this.viewportContentEventListener);
    }
}
