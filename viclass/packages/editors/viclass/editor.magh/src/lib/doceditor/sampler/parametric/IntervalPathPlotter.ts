import { Interval } from 'interval-arithmetic';
import { middle } from '../../evaluator';
import { MyPoint } from '../../common';
import { SegmentType } from '../../common';
import { EuclidianView } from '../../chart';

export class IntervalPathPlotter {
    public static readonly PLOT_MARGIN: number = 1;

    public moveTo = (x: number, y: number): MyPoint => {
        return new MyPoint(x, y, SegmentType.MOVE_TO);
    };

    public lineTo = (x: number, y: number): MyPoint => {
        return new MyPoint(x, y, SegmentType.LINE_TO);
    };

    public segment(x1: number, y1: number, x2: number, y2: number): MyPoint[] {
        return [this.moveTo(x1, y1), this.lineTo(x2, y2)];
    }

    public segmentWithBounds(view: EuclidianView, x1: number, y1: number, x2: number, y2: number): MyPoint[] {
        return this.segmentClipped(view, x1, y1, x2, y2);
    }

    private segmentClipped = (view: EuclidianView, x1: number, y1: number, x2: number, y2: number): MyPoint[] => {
        const yMin = view.getYmin();
        const yMax = view.getYmax();

        if ((y1 < yMin && y2 < yMin) || (y1 > yMax && y2 > yMax)) {
            return [];
        }

        return this.segment(x1, y1, x2, y2);
    };

    public leftToTop = (view: EuclidianView, x: Interval, y: Interval): MyPoint[] => {
        return this.segmentWithBounds(view, x.lo, y.lo, middle(x), view.getYmax());
    };

    public leftToBottom = (view: EuclidianView, x: Interval, y: Interval): MyPoint[] => {
        return this.segmentWithBounds(view, x.lo, y.hi, middle(x), view.getYmin());
    };
}
