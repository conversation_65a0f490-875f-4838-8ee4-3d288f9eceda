import { ComputeEngine } from '@cortex-js/compute-engine';

export class Kernel {
    public static readonly computeEngine = new ComputeEngine();

    /**
     * Maximal number of spreadsheet columns if these are increased above 32000,
     * you need to change traceRow to an number[]
     */
    public static readonly MAX_SPREADSHEET_COLUMNS_DESKTOP: number = 9999;
    /** Maximal number of spreadsheet rows */
    public static readonly MAX_SPREADSHEET_ROWS_DESKTOP: number = 9999;

    /**
     * Maximal number of spreadsheet columns if these are increased above 32000,
     * you need to change traceRow to an number[]
     */
    public static readonly MAX_SPREADSHEET_COLUMNS_WEB: number = 200;
    /** Maximal number of spreadsheet rows */
    public static readonly MAX_SPREADSHEET_ROWS_WEB: number = 350;

    /** string for +- */
    public static readonly STRING_PLUS_MINUS: string = '\u00B1 ';
    /** string for -+ */
    public static readonly STRING_MINUS_PLUS: string = '\u2213 ';

    // G.Sturr 2009-10-18
    // algebra style
    /**
     * @deprecated AlgebraStyle.Value should be used instead.
     * Algebra view style: value */
    public static readonly ALGEBRA_STYLE_VALUE: number = 0;

    /**
     * @deprecated AlgebraStyle.Description should be used instead.
     * Algebra view style: description */
    public static readonly ALGEBRA_STYLE_DESCRIPTION: number = 1;

    /**
     * @deprecated AlgebraStyle.Definition should be used instead.
     * Algebra view style: definition */
    public static readonly ALGEBRA_STYLE_DEFINITION: number = 2;

    /**
     * @deprecated AlgebraStyle.DefinitionAndValue should be used instead.
     * Algebra view style: definition and value */
    public static readonly ALGEBRA_STYLE_DEFINITION_AND_VALUE: number = 3;

    /** Angle unit: radians */
    public static readonly ANGLE_RADIANT: number = 1;
    /** Angle unit: degrees */
    public static readonly ANGLE_DEGREE: number = 2;
    /** Coord system: cartesian */
    public static readonly COORD_CARTESIAN: number = 3;
    /** Coord system: polar */
    public static readonly COORD_POLAR: number = 4;
    /** Coord system: complex numbers */
    public static readonly COORD_COMPLEX: number = 5;
    /** Coord system: 3D cartesian */
    public static readonly COORD_CARTESIAN_3D: number = 6;
    /** Coord system: 3D spherical polar */
    public static readonly COORD_SPHERICAL: number = 7;
    /** Angle type: degrees/minutes/Seconds */
    public static readonly ANGLE_DEGREES_MINUTES_SECONDS: number = 8;

    /** 2*Pi */
    public static readonly PI_2: number = 2.0 * Math.PI;
    /** Pi/2 */
    public static readonly PI_HALF: number = Math.PI / 2.0;
    /** sqrt(1/2) */
    public static readonly SQRT_2_HALF: number = Math.sqrt(2.0) / 2.0;
    /** One degree (Pi/180) */
    public static readonly PI_180: number = Math.PI / 180;
    /** Radian to degree ratio (180/Pi) */
    public static readonly CONST_180_PI: number = 180 / Math.PI;

    /** maximum precision of number numbers */
    public static readonly MAX_DOUBLE_PRECISION: number = 1e-15;
    /** reciprocal of maximum precision of number numbers */
    public static readonly INV_MAX_DOUBLE_PRECISION: number = 1e15;

    /** maximum CAS results cached */
    public static readonly GEOGEBRA_CAS_CACHE_SIZE: number = 500;

    /** print precision */
    public static readonly STANDARD_PRINT_DECIMALS: number = 10;
    /** print precision for Graphing app */
    public static readonly STANDARD_PRINT_DECIMALS_GRAPHING: number = 13;
    /** print precision for Geometry app */
    public static readonly STANDARD_PRINT_DECIMALS_GEOMETRY: number = 1;

    /** print precision for AppConfigDefault */
    public static readonly STANDARD_PRINT_DECIMALS_SHORT: number = 2;

    // style of point/vector coordinates
    /** A = (3, 2) and B = (3; 90^o) */
    public static readonly COORD_STYLE_DEFAULT: number = 0;
    /** A(3|2) and B(3; 90^o) */
    public static readonly COORD_STYLE_AUSTRIAN: number = 1;
    /** A: (3, 2) and B: (3; 90^o) */
    public static readonly COORD_STYLE_FRENCH: number = 2;

    /** standard precision */
    public static readonly STANDARD_PRECISION: number = 1e-8;
    /** square root of standard precision */
    public static readonly STANDARD_PRECISION_SQRT: number = 1e-4;
    /** square of standard precision */
    public static readonly STANDARD_PRECISION_SQUARE: number = 1e-16;
    /** cube of standard precision */
    public static readonly STANDARD_PRECISION_CUBE: number = 1e-24;

    /** minimum precision */
    public static readonly MIN_PRECISION: number = 1e-5;
    /** 1 / (min precision) */
    public static readonly INV_MIN_PRECISION: number = 1e5;

    /** maximum reasonable precision */
    public static readonly MAX_PRECISION: number = 1e-12;
}
