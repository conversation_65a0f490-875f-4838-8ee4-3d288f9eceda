import { MaghDocRenderProp } from '../../model';
import { MyPoint } from '../common/draw/MyPoint';
import { Equation } from '../common/kernel';
import { EuclidianView } from './EuclidianView';

export interface Drawable {
    draw(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void;
}

export interface Destroyable {
    destroy(): void;
}

export interface Sampler extends Destroyable {
    canHandle(eq: Equation): boolean;

    setEquation(eq: Equation): void;

    sampling(view: EuclidianView): MyPoint[];

    drawDebug(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void;
}
