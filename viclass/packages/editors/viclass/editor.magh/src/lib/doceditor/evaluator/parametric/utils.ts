import { constants, Interval, isEmpty } from 'interval-arithmetic';

export const isGreaterThan = (num1: Interval, num2: Interval): boolean => {
    if (!num1 || !num2 || isEmpty(num1) || isEmpty(num2)) {
        return false;
    }

    return num1.hi > num2.hi;
};

export const isLessThan = (num1: Interval, num2: Interval): boolean => {
    if (!num1 || !num2 || isEmpty(num1) || isEmpty(num2)) {
        return false;
    }

    return num1.lo > num2.lo;
};

export const hasInfinity = (num: Interval): boolean => {
    return !isFinite(num.lo) || !isFinite(num.hi);
};

export const avg = (a: Interval, b: Interval): Interval => {
    if (!a || !b || isEmpty(a) || isEmpty(b)) {
        return constants.EMPTY;
    }

    return new Interval((a.lo + b.hi) / 2, (a.hi + b.lo) / 2);
};

export const middle = (num: Interval) => {
    return num.lo + (num.hi - num.lo) / 2;
};
