import {
    CommonToolbar,
    CoordinatorEvent,
    CursorManager,
    CursorMonitor,
    DocumentEditor,
    EditorEventManager,
    EditorFocusCES,
    Tool,
    ToolBar,
    ToolEventData,
    ToolEventListener,
    UserInputHandler,
    VEventListener,
    ViewportFocusInCES,
    ViewportManager,
} from '@viclass/editor.core';
import { DocLoaderCoordinator } from './doc.loader.coordinator';

const LOCAL_CURSOR = 'local_cursor';

/**
 * Handlers and Global Handlers from tools, toolbar needs to be attached and detached
 * at the correct moment. This class listens to the events occurred from the coordinator activities
 * and decide which handlers to be attached.
 *
 * This handler attachment manager is designed to work with multiple concurrent viewport on the same page
 * It uses a single event manager and when a viewport is focused in, the handlers of the toolbars of that viewport
 * will be attached. The toolbar is retrieved from the coordinator.
 *
 * - editor focus
 * - editor blur
 * - tool-focus
 * - tool-blur
 * - viewport-focusin
 * - viewport-focusout
 *
 */
export class HandlerAttachmentManager {
    curEditor: DocumentEditor;
    timeout: any;
    toolListener: ToolEventListener<ToolBar<any, any>, any>;

    em: EditorEventManager;

    commonToolbar?: CommonToolbar;
    editorToolbar?: ToolBar<any, any>;
    focusedVM: ViewportManager;
    focusTool: ToolEventData<ToolBar<any, any>, any>[] = [];

    viewportFocusedStacks: Map<string, ToolEventData<ToolBar<any, any>, any>[]> = new Map();

    private externalMonitored: HTMLElement[] = [];

    cursorManager: CursorManager;
    private cursorMonitor: CursorMonitor;

    constructor(private coordinator: DocLoaderCoordinator) {
        coordinator.coordEventEmitter.registerListener(new this.CoordinatorListener(this));
        this.toolListener = new this.ToolListener(this);
        this.em = coordinator.em;

        // register handler at least once
        this.timeout = setTimeout(() => this.registerHandler());
    }

    additionalMonitoredElements(els: HTMLElement[]) {
        this.externalMonitored = this.externalMonitored.concat(els);

        // if there are an active viewport currently, notify its event manager to capture events for these additional elements
        for (const el of els) {
            this.em.captureEventsFor(el);
        }
    }

    private CoordinatorListener = class implements VEventListener<CoordinatorEvent> {
        constructor(private m: HandlerAttachmentManager) {}

        async onEvent(eventData: CoordinatorEvent): Promise<CoordinatorEvent> {
            switch (eventData.eventType) {
                case 'editor-focus':
                case 'editor-blur':
                    const eS = eventData.state as EditorFocusCES;

                    if (!eS.vmId || eS.vmId != this.m.focusedVM.id) return eventData; // do nothing if this event is for a viewport that this HAM is not in charge of

                    // when an editor is focused, we listen to its tool event
                    const tb = this.m.coordinator.getEditorToolbar(eS.vmId, eS.editor.editorType);

                    if (!tb) return eventData;

                    if (eventData.eventType == 'editor-focus') {
                        tb.registerToolListener(this.m.toolListener);

                        if (tb.curTool) tb.blur(tb.curTool);

                        this.m.editorToolbar = tb;
                    } else {
                        if (tb.curTool) await tb.blur(tb.curTool);
                        tb.unregisterToolListener(this.m.toolListener);

                        delete this.m.editorToolbar;
                    }
                    break;
                case 'viewport-focusin':
                    const veSIn = eventData.state as ViewportFocusInCES;

                    this.m.focusedVM = this.m.coordinator.getViewportManager(veSIn.vmId);

                    // cursor manager
                    this.m.cursorManager = new CursorManager(this.m.focusedVM);
                    this.m.cursorManager.add(LOCAL_CURSOR);
                    this.m.cursorMonitor = new CursorMonitor(this.m.focusedVM, this.m.cursorManager, LOCAL_CURSOR);

                    // retrieve the switch tool and see which editor toolbar is active
                    this.m.commonToolbar = this.m.coordinator.getCommonToolbar(veSIn.vmId);
                    this.m.commonToolbar.registerToolListener(this.m.toolListener);

                    this.m.focusTool = this.m.viewportFocusedStacks.get(veSIn.vmId) || [];

                    break;
                case 'viewport-focusout': {
                    if (!this.m.focusedVM) break;

                    //let veSOut = eventData.state as ViewportFocusOutCES
                    this.m.resetAll();

                    // backup the list of all focus tools of a viewport so that when the viewport is focused in,
                    // we can restore the listening
                    if (this.m.focusTool.length > 0)
                        this.m.viewportFocusedStacks.set(this.m.focusedVM.id, this.m.focusTool);

                    if (this.m.commonToolbar) {
                        // there could be case that focusout occur when the page is just loaded
                        // and the mouse already in the viewport and hence no focusin was set
                        this.m.commonToolbar.unregisterToolListener(this.m.toolListener);
                        delete this.m.commonToolbar;
                    }

                    this.m.cursorMonitor.destroy();
                    this.m.cursorManager.destroy();
                    delete this.m.cursorMonitor;
                    delete this.m.cursorManager;
                    delete this.m.focusedVM;

                    break;
                }

                default: // default, we break out and don't register handler
                    return eventData;
            }

            if (!this.m.timeout) this.m.timeout = setTimeout(() => this.m.registerHandler());

            return eventData;
        }
    };

    /**
     * Handling attachment when tool focus / blur occurs
     */
    private ToolListener = class implements ToolEventListener<ToolBar<any, any>, any> {
        processingFocus: boolean = false;

        constructor(private m: HandlerAttachmentManager) {}

        async onEvent(
            eventData: ToolEventData<ToolBar<any, any>, any>
        ): Promise<ToolEventData<ToolBar<any, any>, any>> {
            if (eventData.eventType == 'change') return eventData;

            const t = eventData.source.getTool(eventData.toolType);
            const s = eventData.source;

            switch (eventData.eventType) {
                case 'focus':
                    if (t.toolbar.isDisabled() || t.toolbar.isToolDisable(t.toolType)) return eventData;

                    // if a tool on the common toolbar is focus, there could be existing tool not on the toolbar being
                    // focus, so we  put this tool of the common toolbar on top.
                    if (s == this.m.commonToolbar) {
                        this.m.focusTool.push(eventData);
                    } else {
                        // otherwise, this is a tool of an editor, in that case, all other existing tool from any other toolbar will be blurred
                        const tobeBlur = [].concat(this.m.focusTool);
                        this.processingFocus = true;
                        for (const e of tobeBlur) {
                            e.source.blur(e.toolType);
                        }
                        this.processingFocus = false;
                        this.m.focusTool = [eventData];
                    }
                    break;

                case 'transient-focus':
                    if (t.toolbar.isDisabled() || t.toolbar.isToolDisable(t.toolType)) return eventData;

                    // if the focus is transient, we don't blur existing tool
                    // so that when the transient focused tool is blurred, we can revert back to the existing tool in the correct order.
                    this.m.focusTool.push(eventData);
                    break;

                case 'blur':
                case 'transient-blur':
                    // if a tool is blur, we remove it from the focus tool and re-register the handler
                    this.m.focusTool = this.m.focusTool.filter(
                        e => e.source != eventData.source || e.toolType != eventData.toolType
                    );

                    break;
            }

            if (!this.m.timeout) this.m.timeout = setTimeout(() => this.m.registerHandler());

            return eventData;
        }
    };

    private resetAll() {
        this.em.resetKeyboardHandling();
        this.em.resetMouseHandling();
        this.em.resetPointerHandling();
    }

    private registerHandler() {
        this.resetAll();

        delete this.timeout;

        const em = this.em;
        if (!em) return;

        const focusTools: UserInputHandler[] = this.focusTool.map(e => e.source.getTool(e.toolType));
        const handlerStack: UserInputHandler[] = [this.commonToolbar, this.editorToolbar, ...focusTools];

        // register all handling, except for the global ones
        for (const uih of handlerStack) {
            if (uih) {
                if (uih.mouseHandler)
                    for (const mh of uih.mouseHandling) if (!mh.global) em.registerMouseHandling(mh, uih.mouseHandler);

                if (uih.pointerHandler)
                    for (const mh of uih.pointerHandling)
                        if (!mh.global) em.registerPointerHandling(mh, uih.pointerHandler);

                if (uih.keyboardHandler)
                    for (const kh of uih.keyboardHandling)
                        if (!kh.global) em.registerKeyboardHandling(kh, uih.keyboardHandler);
            }
        }

        if (this.commonToolbar) this.registerGlobal(em, this.commonToolbar); // register all global handling of the coordinator toolbar
        if (this.editorToolbar) this.registerGlobal(em, this.editorToolbar); // if there is a focus editor, register all global handling of the tools of that editor

        // update the tool stack in cursor manager so that it knows if focused handler has been changed
        this.cursorMonitor?.updateToolStack(handlerStack.map(h => h?.pointerHandler).filter(h => h != undefined));
    }

    private registerGlobal(em: EditorEventManager, toolbar: ToolBar<any, Tool>) {
        if (toolbar.isDisabled()) return;
        // All global handling of the all tools within a toolbar
        toolbar.tools.forEach((tool, type) => {
            if (tool.mouseHandler) {
                for (const mh of tool.mouseHandling) {
                    if (mh.global) em.registerMouseHandling(mh, tool.mouseHandler);
                }
            }

            if (tool.pointerHandler)
                for (const mh of tool.pointerHandling)
                    if (mh.global) em.registerPointerHandling(mh, tool.pointerHandler);

            if (tool.keyboardHandler) {
                for (const kh of tool.keyboardHandling) {
                    if (kh.global) em.registerKeyboardHandling(kh, tool.keyboardHandler);
                }
            }
        });
    }
}
