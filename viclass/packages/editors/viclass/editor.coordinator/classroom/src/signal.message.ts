/**
 *
 * <AUTHOR>
 */

export type NotificationSignalMessageType =
    | 'AcceptRaiseHandND'
    | 'AcceptShareScreenND'
    | 'LeaveClassND'
    | 'RaiseHandND'
    | 'ReqShareScreenND'
    | 'RegisterND'
    | 'RegistrationCancelledND'
    | 'StopPresentationND'
    | 'AcceptPresentationRequestND'
    | 'AcceptRegistrationND'
    | 'NewQuestionND'
    | 'RejectPresentationRequestND'
    | 'RejectRegistrationND'
    | 'RequestPresentationND'
    | 'RejectRaiseHandND'
    | 'RejectShareScreenND'
    | 'CancelRaiseHandND'
    | 'CancelShareScreenND'
    | 'ShareScreenRemovedND'
    | 'JoinClassND'
    | 'StartClassND'
    | 'StopClassND'
    | 'StopQuestionND'
    | 'CancelPresentationRequestND'
    | 'PinnedCoordStateND'
    | 'UnpinnedCoordStateND'
    | 'RequestPinTabND'
    | 'CancelRequestPinTabND'
    | 'RejectRequestPinTabND'
    | 'ApproveRequestPinTabND'
    | 'UpdateRequestPinTabND'
    | 'UserAvailableStatusChangedND';

export type SignalMessageType =
    | NotificationSignalMessageType
    | 'requestOffer'
    | 'offer'
    | 'iceCandidate'
    | 'answer'
    | 'kickOut'
    | 'InsertDocResponse'
    | 'InsertLayerResponse'
    | 'UpdateLayerResponse'
    | 'presentCoordState'
    | 'renamedCoordState'
    | 'RTCConnectionChange'
    | 'DocInfoUpdated'
    | 'DocInfoDeleted';

export interface SignalMessage {
    requestId?: string;
    fromPeer: string;
    toPeer: string;
    signalType: SignalMessageType;
    data: any;
}

export interface SignalMessageListener {
    readonly signalMessageType: SignalMessageType[];
    onMessage(msg: SignalMessage): Promise<void>;
    onRequest(msg: SignalMessage): Promise<SignalMessage>;
}

export interface Signaler {
    registerListener(listener: SignalMessageListener);
}
