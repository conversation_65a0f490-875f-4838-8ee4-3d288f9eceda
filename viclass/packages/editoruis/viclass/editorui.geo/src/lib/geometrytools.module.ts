import { DragDropModule } from '@angular/cdk/drag-drop';
import { Overlay, OverlayContainer, OverlayModule } from '@angular/cdk/overlay';
import { PortalModule } from '@angular/cdk/portal';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSliderModule } from '@angular/material/slider';
import { EditorUILoaderComponent } from '@viclass/editorui.loader';
import { CommandtoolComponent } from './commandtool/commandtool.component';
import { ConstrainRendererComponent } from './commandtool/constrain-renderer/constrain-renderer.component';
import { HeighlightPipe } from './commandtool/heighlight.pipe';
import { ParamInputComponent } from './commandtool/param-input/param-input.component';
import { GeometrytoolsComponent } from './geometrytools.component';
import { ListElementToolComponent } from './list-element-tool/list.element.tool.component';
import { NamingElementInputComponent } from './naming-element-input/naming-element-input.component';
import { RegularPolygonEdgesInputComponent } from './regular-polygon-edges-input/regular-polygon-edges-input.component';
import { SettingToolAdjustNumberComponent } from './setting-tool/setting-tool-adjust-number/setting-tool-adjust-number.component';
import { SettingToolColorsComponent } from './setting-tool/setting-tool-colors/setting-tool-colors.component';
import { SettingToolSwitchComponent } from './setting-tool/setting-tool-switch/setting-tool-switch.component';
import { SettingToolComponent } from './setting-tool/setting-tool.component';
import { ToolbarButtonComponent } from './toolbar-button/toolbar-button.component';
import { TooltipComponent } from './tooltip/tooltip.component';

@NgModule({
    declarations: [
        GeometrytoolsComponent,
        ToolbarButtonComponent,
        RegularPolygonEdgesInputComponent,
        NamingElementInputComponent,
        SettingToolComponent,
        SettingToolAdjustNumberComponent,
        SettingToolColorsComponent,
        SettingToolSwitchComponent,
    ],
    imports: [
        CommonModule,
        OverlayModule,
        MatAutocompleteModule,
        MatButtonModule,
        MatMenuModule,
        MatSliderModule,
        ReactiveFormsModule,
        FormsModule,
        DragDropModule,
        MatDialogModule,
        TooltipComponent,
        PortalModule,
        MatSlideToggleModule,
        ListElementToolComponent,
        HeighlightPipe,
        ParamInputComponent,
        ConstrainRendererComponent,
        CommandtoolComponent,
    ],
    providers: [
        {
            provide: OverlayContainer,
            useFactory: (comp: EditorUILoaderComponent) => {
                return comp.overlayContainer;
            },
            deps: [EditorUILoaderComponent],
        },
        { provide: Overlay, useClass: Overlay },
    ],
})
export class GeometryToolsModule {}
